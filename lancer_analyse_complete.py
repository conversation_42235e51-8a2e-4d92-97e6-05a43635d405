#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LANCEUR D'ANALYSE COMPLÈTE - BACCARAT INDEX SYSTEM
Lance l'analyse complète avec les deux analyseurs optimisés
"""

import subprocess
import sys
import os
import psutil
from datetime import datetime

def verifier_ressources_systeme():
    """Vérifie les ressources système disponibles"""
    print("🔍 VÉRIFICATION DES RESSOURCES SYSTÈME")
    print("-" * 50)
    
    # RAM disponible
    ram_total = psutil.virtual_memory().total / (1024**3)
    ram_disponible = psutil.virtual_memory().available / (1024**3)
    
    print(f"💾 RAM totale : {ram_total:.1f} GB")
    print(f"💾 RAM disponible : {ram_disponible:.1f} GB")
    
    # CPU
    nb_coeurs = psutil.cpu_count()
    print(f"🖥️ Nombre de cœurs CPU : {nb_coeurs}")
    
    # Espace disque
    espace_libre = psutil.disk_usage('.').free / (1024**3)
    print(f"💽 Espace disque libre : {espace_libre:.1f} GB")
    
    # Vérifications
    warnings = []
    if ram_disponible < 20:
        warnings.append(f"⚠️ RAM disponible faible ({ram_disponible:.1f} GB < 20 GB recommandés)")
    
    if nb_coeurs < 4:
        warnings.append(f"⚠️ Nombre de cœurs faible ({nb_coeurs} < 4 recommandés)")
    
    if espace_libre < 5:
        warnings.append(f"⚠️ Espace disque faible ({espace_libre:.1f} GB < 5 GB recommandés)")
    
    if warnings:
        print("\n⚠️ AVERTISSEMENTS :")
        for warning in warnings:
            print(f"   {warning}")
        print("\n💡 L'analyse peut être plus lente ou échouer avec ces limitations")
    else:
        print("\n✅ Ressources système suffisantes pour l'analyse haute performance")
    
    return ram_disponible >= 15, nb_coeurs >= 2, espace_libre >= 2

def verifier_fichier_dataset(filename):
    """Vérifie l'existence et la taille du fichier dataset"""
    print(f"\n🔍 VÉRIFICATION DU DATASET : {filename}")
    print("-" * 50)
    
    if not os.path.exists(filename):
        print(f"❌ Fichier non trouvé : {filename}")
        return False
    
    taille_gb = os.path.getsize(filename) / (1024**3)
    print(f"📏 Taille du fichier : {taille_gb:.2f} GB")
    
    if taille_gb < 1:
        print("⚠️ Fichier petit (< 1 GB) - analyse rapide attendue")
    elif taille_gb > 10:
        print("⚠️ Fichier très volumineux (> 10 GB) - analyse longue attendue")
    else:
        print("✅ Taille de fichier optimale pour l'analyse")
    
    return True

def lancer_analyseur_biais(filename):
    """Lance l'analyseur de biais systématique"""
    print(f"\n🚀 LANCEMENT DE L'ANALYSEUR DE BIAIS SYSTÉMATIQUE")
    print("=" * 80)
    
    try:
        cmd = [sys.executable, "analyseur_biais_systematique.py", filename]
        print(f"📋 Commande : {' '.join(cmd)}")
        
        debut = datetime.now()
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        fin = datetime.now()
        duree = fin - debut
        
        print(f"⏱️ Durée d'exécution : {duree}")
        
        if result.returncode == 0:
            print("✅ Analyseur de biais terminé avec succès")
            return True
        else:
            print("❌ Erreur dans l'analyseur de biais :")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du lancement : {e}")
        return False

def lancer_analyseur_statistique(filename):
    """Lance l'analyseur statistique avancé"""
    print(f"\n🚀 LANCEMENT DE L'ANALYSEUR STATISTIQUE AVANCÉ")
    print("=" * 80)
    
    try:
        cmd = [sys.executable, "analyseur_statistique_avance.py", filename]
        print(f"📋 Commande : {' '.join(cmd)}")
        
        debut = datetime.now()
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        fin = datetime.now()
        duree = fin - debut
        
        print(f"⏱️ Durée d'exécution : {duree}")
        
        if result.returncode == 0:
            print("✅ Analyseur statistique terminé avec succès")
            return True
        else:
            print("❌ Erreur dans l'analyseur statistique :")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du lancement : {e}")
        return False

def lister_rapports_generes():
    """Liste les rapports générés"""
    print(f"\n📄 RAPPORTS GÉNÉRÉS")
    print("-" * 50)
    
    rapports = []
    for fichier in os.listdir('.'):
        if fichier.startswith('rapport_') and fichier.endswith('.txt'):
            taille = os.path.getsize(fichier) / 1024  # KB
            rapports.append((fichier, taille))
    
    if rapports:
        rapports.sort(key=lambda x: os.path.getmtime(x[0]), reverse=True)  # Trier par date
        for fichier, taille in rapports:
            print(f"📋 {fichier} ({taille:.1f} KB)")
    else:
        print("❌ Aucun rapport trouvé")
    
    return len(rapports)

def main():
    if len(sys.argv) != 2:
        print("Usage: python lancer_analyse_complete.py <fichier_dataset.json>")
        print("\nExemple :")
        print("python lancer_analyse_complete.py dataset_baccarat_lupasco_20250704_170242_condensed.json")
        sys.exit(1)
    
    filename = sys.argv[1]
    
    print("🎯 LANCEUR D'ANALYSE COMPLÈTE - BACCARAT INDEX SYSTEM")
    print("=" * 80)
    print(f"Dataset : {filename}")
    print(f"Heure de début : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Vérifications préliminaires
    ram_ok, cpu_ok, disk_ok = verifier_ressources_systeme()
    
    if not verifier_fichier_dataset(filename):
        sys.exit(1)
    
    if not (ram_ok and cpu_ok and disk_ok):
        reponse = input("\n⚠️ Ressources limitées détectées. Continuer quand même ? (o/N) : ")
        if reponse.lower() not in ['o', 'oui', 'y', 'yes']:
            print("❌ Analyse annulée par l'utilisateur")
            sys.exit(1)
    
    # Lancement des analyses
    debut_global = datetime.now()
    
    print(f"\n🚀 DÉBUT DE L'ANALYSE COMPLÈTE")
    print("=" * 80)
    
    # 1. Analyseur de biais systématique
    succes_biais = lancer_analyseur_biais(filename)
    
    # 2. Analyseur statistique avancé
    succes_statistique = lancer_analyseur_statistique(filename)
    
    # Résumé final
    fin_global = datetime.now()
    duree_totale = fin_global - debut_global
    
    print(f"\n🏁 ANALYSE COMPLÈTE TERMINÉE")
    print("=" * 80)
    print(f"⏱️ Durée totale : {duree_totale}")
    print(f"📊 Analyseur de biais : {'✅ Succès' if succes_biais else '❌ Échec'}")
    print(f"📊 Analyseur statistique : {'✅ Succès' if succes_statistique else '❌ Échec'}")
    
    # Lister les rapports générés
    nb_rapports = lister_rapports_generes()
    
    if nb_rapports > 0:
        print(f"\n🎉 ANALYSE TERMINÉE AVEC SUCCÈS !")
        print(f"📄 {nb_rapports} rapport(s) généré(s)")
        print("\n💡 Consultez les fichiers rapport_*.txt pour les résultats détaillés")
    else:
        print(f"\n⚠️ Analyse terminée mais aucun rapport généré")
        print("💡 Vérifiez les erreurs ci-dessus")

if __name__ == "__main__":
    main()
