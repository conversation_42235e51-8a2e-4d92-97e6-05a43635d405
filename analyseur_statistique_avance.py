#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR STATISTIQUE AVANCÉ - BACCARAT INDEX SYSTEM
Optimisé pour 28GB RAM + 8 cœurs CPU
Analyse statistique de niveau doctoral avec métriques avancées
"""

import json
import sys
import numpy as np
import pandas as pd
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Any
import statistics
from datetime import datetime
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import gc
import psutil
import warnings
warnings.filterwarnings('ignore')

def creer_dict_imbrique():
    """Crée un dictionnaire imbriqué compatible avec multiprocessing"""
    return defaultdict(int)

def creer_dict_patterns():
    """Crée un dictionnaire de patterns compatible avec multiprocessing"""
    return {'banker': [], 'player': [], 'tie': []}

# Import de la classe de base pour l'analyse par positions
try:
    from analyseur_biais_systematique import AnalyseurParPositions
except ImportError:
    # Fallback si l'import échoue
    class AnalyseurParPositions:
        @staticmethod
        def extraire_donnees_par_positions(donnees):
            return {}
        @staticmethod
        def filtrer_mains_valides_statique(mains):
            return []

class AnalyseurStatistiqueAvanceParPositions:
    """Méthodes statiques pour l'analyse statistique avancée par positions"""

    @staticmethod
    def analyser_position_distributions(pos: int, data: Dict) -> Dict:
        """Analyse les distributions statistiques pour une position donnée"""
        patterns_data = {}

        for main in data['mains_actuelles']:
            if main.get('index1') is not None and main.get('index2') and main.get('index3'):
                pattern = f"{main['index1']}_{main['index2']}"
                index3 = main['index3'].lower()

                # Initialiser le pattern s'il n'existe pas
                if pattern not in patterns_data:
                    patterns_data[pattern] = {'banker': [], 'player': [], 'tie': []}

                if index3 in patterns_data[pattern]:
                    patterns_data[pattern][index3].append(1)  # Valeur symbolique pour comptage

        # Calculer les statistiques pour chaque pattern
        resultats = {}
        for pattern, data_pattern in patterns_data.items():
            resultats[pattern] = {}
            for outcome in ['banker', 'player', 'tie']:
                values = data_pattern[outcome]
                if values:
                    resultats[pattern][f'{outcome}_count'] = len(values)
                    resultats[pattern][f'{outcome}_mean'] = np.mean(values) if values else 0
                    resultats[pattern][f'{outcome}_std'] = np.std(values) if len(values) > 1 else 0
                else:
                    resultats[pattern][f'{outcome}_count'] = 0
                    resultats[pattern][f'{outcome}_mean'] = 0
                    resultats[pattern][f'{outcome}_std'] = 0

        return {
            'position': pos,
            'distributions': resultats,
            'total_observations': len(data['mains_actuelles'])
        }

    @staticmethod
    def analyser_position_volatilite(pos: int, data: Dict) -> Dict:
        """Analyse la volatilité des patterns pour une position donnée"""
        sequences_par_pattern = defaultdict(list)

        # Grouper les séquences par pattern
        for main in data['mains_actuelles']:
            if main.get('index1') is not None and main.get('index2') and main.get('index3'):
                pattern = f"{main['index1']}_{main['index2']}"
                index3 = main['index3']
                sequences_par_pattern[pattern].append(index3)

        # Calculer la volatilité pour chaque pattern
        volatilite_patterns = {}
        for pattern, sequence in sequences_par_pattern.items():
            if len(sequence) > 1:
                # Convertir en valeurs numériques pour calcul de volatilité
                values = []
                for outcome in sequence:
                    if outcome == 'BANKER':
                        values.append(1)
                    elif outcome == 'PLAYER':
                        values.append(-1)
                    else:  # TIE
                        values.append(0)

                volatilite_patterns[pattern] = {
                    'variance': np.var(values),
                    'std_dev': np.std(values),
                    'range': max(values) - min(values) if values else 0,
                    'count': len(values)
                }
            else:
                volatilite_patterns[pattern] = {
                    'variance': 0,
                    'std_dev': 0,
                    'range': 0,
                    'count': len(sequence)
                }

        return {
            'position': pos,
            'volatilite': volatilite_patterns,
            'total_patterns': len(volatilite_patterns)
        }

class AnalyseurStatistiqueAvance:
    def __init__(self, max_ram_gb: int = 28, nb_coeurs: int = 8):
        """
        Analyseur statistique haute performance
        
        Args:
            max_ram_gb: RAM maximale à utiliser (défaut: 28GB)
            nb_coeurs: Nombre de cœurs CPU à utiliser (défaut: 8)
        """
        self.max_ram_gb = max_ram_gb
        self.nb_coeurs = nb_coeurs
        self.donnees = None
        self.cache_transitions = {}
        self.cache_statistiques = {}
        
        # Configuration multiprocessing
        mp.set_start_method('spawn', force=True)
        
        print(f"🚀 ANALYSEUR STATISTIQUE AVANCÉ INITIALISÉ")
        print(f"   • RAM allouée : {max_ram_gb} GB")
        print(f"   • Cœurs CPU : {nb_coeurs}")
        print(f"   • RAM système disponible : {psutil.virtual_memory().available / (1024**3):.1f} GB")
        
    def charger_donnees_haute_performance(self, filename: str) -> bool:
        """Charge le dataset JSON avec gestion automatique de la taille"""
        try:
            import os

            # Vérifier la taille du fichier
            file_size = os.path.getsize(filename)
            file_size_gb = file_size / (1024**3)

            print(f"🔍 Chargement du dataset : {filename}")
            print(f"📏 Taille du fichier : {file_size_gb:.2f} GB")

            # Si le fichier fait plus de 5GB, utiliser le cache haute performance
            if file_size_gb > 5.0:
                print("🚀 Fichier volumineux détecté - Chargement haute performance avec cache 10GB RAM")
                return self._charger_donnees_cache_10gb(filename)
            else:
                print("📖 Chargement standard")
                with open(filename, 'r', encoding='utf-8') as f:
                    self.donnees = json.load(f)

                if 'parties_condensees' in self.donnees:
                    print(f"✅ Dataset condensé chargé : {len(self.donnees['parties_condensees'])} parties")
                    return True
                else:
                    print("❌ Format de dataset non reconnu")
                    return False

        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            return False

    def _charger_donnees_cache_10gb(self, filename: str) -> bool:
        """
        Chargement haute performance avec allocation de 10GB de RAM
        Optimisé pour les très gros fichiers JSON (9-12GB)
        """
        import gc
        import sys

        try:
            print("🧠 Allocation de 10GB de RAM pour le cache...")

            # Optimisations système pour performances maximales
            import os
            os.environ['PYTHONHASHSEED'] = '0'  # Hash déterministe

            # Forcer le garbage collection avant le chargement
            gc.collect()
            gc.disable()  # Désactiver GC pendant le chargement pour plus de vitesse

            # Lire tout le fichier en une fois avec un buffer optimisé
            print("📖 Lecture complète du fichier en mémoire...")

            # Obtenir la taille du fichier pour la barre de progression
            file_size = os.path.getsize(filename)

            with open(filename, 'r', encoding='utf-8', buffering=512*1024*1024) as f:  # Buffer 512MB optimisé
                print("⚡ Chargement du contenu complet...")
                print("📊 Progression du chargement :")

                # Lire par chunks pour afficher la progression
                content = ""
                chunk_size = 50 * 1024 * 1024  # 50MB par chunk pour progression
                bytes_read = 0

                while True:
                    chunk = f.read(chunk_size)
                    if not chunk:
                        break
                    content += chunk
                    bytes_read += len(chunk.encode('utf-8'))

                    # Afficher progression
                    progress = min(100, (bytes_read / file_size) * 100)
                    print(f"   📈 {progress:.1f}% chargé ({bytes_read:,} / {file_size:,} octets)", end='\r')

                print()  # Nouvelle ligne après progression

            print(f"✅ Fichier chargé en mémoire : {len(content):,} caractères")

            # Analyser le début du contenu pour détecter le format
            debut_content = content[:200]
            print(f"🔍 Début du fichier : {debut_content[:100]}...")

            # Détecter et corriger le format si nécessaire
            if content.startswith(',{"partie_number"'):
                print("🔧 Correction du format JSON mal formé (commence par virgule)...")
                if content.endswith(']}'):
                    content = content[:-2]  # Enlever ]}
                    print("🔧 Suppression du ]} final en trop")
                # Enlever la virgule du début et ajouter la structure JSON correcte
                content = '{"parties_condensees": [' + content[1:] + ']}'
                print("✅ Format JSON corrigé")

            elif content.startswith('{"partie_number"'):
                print("🔧 Correction du format JSON (array d'objets sans wrapper)...")
                # Ajouter le wrapper pour format condensé
                content = '{"parties_condensees": [' + content + ']}'
                print("✅ Format JSON corrigé")

            elif '"parties_condensees"' in debut_content:
                print("✅ Format JSON condensé détecté (correct)")

            elif '"parties"' in debut_content:
                print("✅ Format JSON standard détecté (correct)")

            else:
                print("🔍 Analyse approfondie du format...")
                if '{"partie_number":' in content[:1000]:
                    print("🔧 Pattern partie détecté, correction du format...")
                    start_idx = content.find('{"partie_number":')
                    if start_idx != -1:
                        content = '{"parties_condensees": [' + content[start_idx:] + ']}'
                        print("✅ Format JSON corrigé avec pattern détecté")

            # Parser le JSON avec le contenu corrigé
            print("🔄 Parsing JSON en cours...")
            data = json.loads(content)

            # Libérer la mémoire du contenu brut
            del content
            gc.enable()  # Réactiver GC après chargement
            gc.collect()

            # Traiter les données
            if 'parties_condensees' in data:
                parties = data['parties_condensees']
                print("📊 Format détecté : Condensé (cache 10GB)")
                print(f"📊 Métadonnées : Format condensé sans métadonnées détaillées")
            elif 'parties' in data:
                parties = data['parties']
                print("📊 Format détecté : Standard (cache 10GB)")
            else:
                print("❌ Structure JSON invalide après correction")
                print(f"🔑 Clés disponibles : {list(data.keys())}")
                return False

            # Stocker les données
            self.donnees = data

            print(f"✅ Dataset chargé avec succès en mode cache 10GB !")
            print(f"   • Total parties traitées : {len(parties):,}")

            return True

        except MemoryError:
            print("❌ Erreur : Mémoire insuffisante pour le cache 10GB")
            print("💡 Suggestion : Fermer d'autres applications ou augmenter la RAM")
            return False
        except Exception as e:
            print(f"❌ Erreur lors du chargement cache 10GB : {e}")
            import traceback
            print(f"🔍 Traceback : {traceback.format_exc()}")
            return False

    def filtrer_mains_valides(self, mains_condensees):
        """Filtre les mains dummy et invalides - cohérent avec analyseur_biais_systematique"""
        return [main for main in mains_condensees
                if main.get('main_number') is not None
                and main.get('index1') is not None
                and main.get('index1') != ''
                and main.get('index2') not in ['', 'dummy']
                and main.get('index3') not in ['', 'DUMMY']]

    def calculer_statistiques_avancees(self, data: List[float]) -> Dict[str, float]:
        """Calcule des statistiques avancées sur un échantillon"""
        if not data or len(data) < 2:
            return {}
        
        data_array = np.array(data)
        
        # Statistiques de base
        moyenne = np.mean(data_array)
        mediane = np.median(data_array)
        ecart_type = np.std(data_array, ddof=1)  # Écart-type corrigé
        variance = np.var(data_array, ddof=1)    # Variance corrigée
        
        # Statistiques de forme
        from scipy import stats
        skewness = stats.skew(data_array)        # Asymétrie
        kurtosis = stats.kurtosis(data_array)    # Aplatissement
        
        # Quantiles
        q1 = np.percentile(data_array, 25)
        q3 = np.percentile(data_array, 75)
        iqr = q3 - q1                            # Écart interquartile
        
        # Statistiques robustes
        mad = stats.median_abs_deviation(data_array)  # Déviation absolue médiane
        
        # Coefficient de variation
        cv = (ecart_type / moyenne) * 100 if moyenne != 0 else 0
        
        # Entropie de Shannon (approximation)
        hist, _ = np.histogram(data_array, bins=50, density=True)
        hist = hist[hist > 0]  # Éliminer les zéros
        entropie = -np.sum(hist * np.log2(hist)) if len(hist) > 0 else 0
        
        return {
            'moyenne': moyenne,
            'mediane': mediane,
            'ecart_type': ecart_type,
            'variance': variance,
            'skewness': skewness,
            'kurtosis': kurtosis,
            'q1': q1,
            'q3': q3,
            'iqr': iqr,
            'mad': mad,
            'cv': cv,
            'entropie': entropie,
            'min': np.min(data_array),
            'max': np.max(data_array),
            'etendue': np.max(data_array) - np.min(data_array),
            'n': len(data_array)
        }
    
    def analyser_transitions_parallele(self) -> Dict[str, Any]:
        """Analyse RÉVOLUTIONNAIRE des transitions par position parallèle"""
        print("\n🚀 ANALYSE RÉVOLUTIONNAIRE DES TRANSITIONS (PAR POSITIONS)")
        print("=" * 80)

        # Extraire les données par positions
        print("📊 Extraction des données par positions...")
        positions_data = AnalyseurParPositions.extraire_donnees_par_positions(self.donnees)
        positions_avec_donnees = {pos: data for pos, data in positions_data.items()
                                 if len(data['mains_actuelles']) > 0}

        print(f"📈 {len(positions_avec_donnees)} positions avec données à analyser")

        # Compteurs globaux pour agréger les résultats
        transitions_globales = defaultdict(creer_dict_imbrique)

        # Traitement parallèle des positions
        nb_coeurs = min(self.nb_coeurs, len(positions_avec_donnees))
        print(f"🔥 Utilisation de {nb_coeurs} cœurs pour {len(positions_avec_donnees)} positions")

        with ProcessPoolExecutor(max_workers=nb_coeurs) as executor:
            futures = []
            for pos, data in positions_avec_donnees.items():
                future = executor.submit(AnalyseurParPositions.analyser_position_transitions, pos, data)
                futures.append(future)

            # Collecter et agréger les résultats
            for i, future in enumerate(futures):
                try:
                    resultat = future.result()

                    # Agréger les patterns séquentiels
                    for pattern, count in resultat['sequences_patterns'].items():
                        transitions_globales[pattern]['count'] += count

                    if (i + 1) % 10 == 0:
                        print(f"   ✅ {i + 1}/{len(futures)} positions traitées")

                except Exception as e:
                    print(f"   ⚠️ Erreur position {i}: {e}")

        print(f"✅ Analyse parallèle révolutionnaire terminée")
        return dict(transitions_globales)
    
    @staticmethod
    def _analyser_chunk_transitions(chunk_parties: List[Dict], chunk_id: int) -> Dict[str, Dict[str, int]]:
        """Analyse un chunk de parties (méthode statique pour multiprocessing)"""
        transitions_chunk = defaultdict(creer_dict_imbrique)
        
        for partie in chunk_parties:
            # Filtrer les mains dummy et invalides
            mains_valides = [main for main in partie['mains_condensees']
                            if main.get('main_number') is not None
                            and main.get('index1') is not None
                            and main.get('index1') != ''
                            and main.get('index2') not in ['', 'dummy']
                            and main.get('index3') not in ['', 'DUMMY']]

            for i in range(len(mains_valides) - 1):
                main_n = mains_valides[i]
                main_n_plus_1 = mains_valides[i + 1]
                
                if (main_n.get('index1') is not None and main_n.get('index1') != "" and
                    main_n.get('index2') and main_n.get('index3') and
                    main_n_plus_1.get('index1') is not None and main_n_plus_1.get('index1') != "" and
                    main_n_plus_1.get('index2') and main_n_plus_1.get('index3')):
                    
                    combo_key = f"{main_n['index1']}_{main_n['index2']}"
                    resultat_n_plus_1 = main_n_plus_1['index3']
                    transitions_chunk[combo_key][resultat_n_plus_1] += 1
        
        return dict(transitions_chunk)

    @staticmethod
    def _extraire_donnees_correlation_position(pos: int, data: Dict) -> List[List[int]]:
        """Extrait les données numériques pour corrélation d'une position donnée"""
        donnees_position = []

        for main in data['mains_actuelles']:
            if (main.get('index1') is not None and main.get('index1') != "" and
                main.get('index2') and main.get('index3')):

                # Encodage numérique
                index1_num = int(main['index1'])
                index2_num = {'A': 0, 'B': 1, 'C': 2}[main['index2']]
                index3_num = {'BANKER': 0, 'PLAYER': 1, 'TIE': 2}[main['index3']]

                donnees_position.append([index1_num, index2_num, index3_num])

        return donnees_position

    @staticmethod
    def analyser_position_tests_statistiques(pos: int, data: Dict) -> Dict:
        """Analyse les tests statistiques pour une position donnée"""
        transitions = defaultdict(creer_dict_imbrique)

        for main in data['mains_actuelles']:
            if (main.get('index1') is not None and main.get('index1') != "" and
                main.get('index2') and main.get('index3')):

                # Créer le pattern INDEX1_INDEX2
                pattern = f"{main['index1']}_{main['index2']}"
                outcome = main['index3']

                transitions[pattern][outcome] += 1

        return {
            'position': pos,
            'transitions': dict(transitions)
        }
    
    def calculer_matrices_correlation_avancees(self) -> Dict[str, np.ndarray]:
        """Calcul RÉVOLUTIONNAIRE des matrices de corrélation par position parallèle"""
        print("\n🚀 CALCUL RÉVOLUTIONNAIRE DES MATRICES DE CORRÉLATION (PAR POSITIONS)")
        print("=" * 80)

        # Extraire les données par positions
        positions_data = AnalyseurParPositions.extraire_donnees_par_positions(self.donnees)
        positions_avec_donnees = {pos: data for pos, data in positions_data.items()
                                 if len(data['mains_actuelles']) > 0}

        # Liste globale pour agréger les données
        donnees_analyse = []

        # Traitement parallèle des positions
        nb_coeurs = min(self.nb_coeurs, len(positions_avec_donnees))
        print(f"🔥 Utilisation de {nb_coeurs} cœurs pour {len(positions_avec_donnees)} positions")

        with ProcessPoolExecutor(max_workers=nb_coeurs) as executor:
            futures = []
            for pos, data in positions_avec_donnees.items():
                future = executor.submit(self._extraire_donnees_correlation_position, pos, data)
                futures.append(future)

            # Collecter et agréger les résultats
            for i, future in enumerate(futures):
                try:
                    donnees_position = future.result()
                    donnees_analyse.extend(donnees_position)

                    if (i + 1) % 10 == 0:
                        print(f"   ✅ {i + 1}/{len(futures)} positions traitées")

                except Exception as e:
                    print(f"   ⚠️ Erreur position {i}: {e}")

        # Conversion en DataFrame pandas pour analyse
        df = pd.DataFrame(donnees_analyse, columns=['INDEX1', 'INDEX2', 'INDEX3'])

        print(f"✅ Analyse parallèle terminée")
        print(f"📊 Données pour corrélation : {len(df):,} observations")

        # Matrice de corrélation de Pearson
        corr_pearson = df.corr()

        # Matrice de corrélation de Spearman (rang)
        corr_spearman = df.corr(method='spearman')

        # Matrice de corrélation de Kendall (tau)
        corr_kendall = df.corr(method='kendall')

        print("✅ Matrices de corrélation révolutionnaires calculées")

        return {
            'pearson': corr_pearson.values,
            'spearman': corr_spearman.values,
            'kendall': corr_kendall.values,
            'labels': ['INDEX1', 'INDEX2', 'INDEX3']
        }
    
    def analyser_distributions_par_pattern(self) -> Dict[str, Dict[str, float]]:
        """Analyse RÉVOLUTIONNAIRE des distributions par pattern par position parallèle"""
        print("\n🚀 ANALYSE RÉVOLUTIONNAIRE DES DISTRIBUTIONS PAR PATTERN (PAR POSITIONS)")
        print("=" * 80)

        # Extraire les données par positions
        positions_data = AnalyseurParPositions.extraire_donnees_par_positions(self.donnees)
        positions_avec_donnees = {pos: data for pos, data in positions_data.items()
                                 if len(data['mains_actuelles']) > 0}

        print(f"📈 {len(positions_avec_donnees)} positions avec données à analyser")

        # Compteurs globaux pour agréger les résultats
        patterns_data_globaux = {}

        # Traitement parallèle des positions
        nb_coeurs = min(self.nb_coeurs, len(positions_avec_donnees))
        print(f"🔥 Utilisation de {nb_coeurs} cœurs pour {len(positions_avec_donnees)} positions")

        with ProcessPoolExecutor(max_workers=nb_coeurs) as executor:
            futures = []
            for pos, data in positions_avec_donnees.items():
                future = executor.submit(AnalyseurStatistiqueAvanceParPositions.analyser_position_distributions, pos, data)
                futures.append(future)

            # Collecter et agréger les résultats
            for i, future in enumerate(futures):
                try:
                    resultat = future.result()

                    # Agréger les distributions
                    for pattern, stats in resultat['distributions'].items():
                        # Initialiser le pattern s'il n'existe pas
                        if pattern not in patterns_data_globaux:
                            patterns_data_globaux[pattern] = {'banker': [], 'player': [], 'tie': []}

                        for outcome in ['banker', 'player', 'tie']:
                            count = stats.get(f'{outcome}_count', 0)
                            # Ajouter les observations (1 pour chaque occurrence)
                            patterns_data_globaux[pattern][outcome].extend([1] * count)

                    if (i + 1) % 10 == 0:
                        print(f"   ✅ {i + 1}/{len(futures)} positions traitées")

                except Exception as e:
                    print(f"   ⚠️ Erreur position {i}: {e}")

        # Calculer les statistiques avancées pour chaque pattern
        resultats_patterns = {}

        for pattern, data in patterns_data_globaux.items():
            print(f"📊 Analyse pattern {pattern}...")

            resultats_patterns[pattern] = {}

            for resultat, values in data.items():
                if values:
                    stats = self.calculer_statistiques_avancees(values)
                    resultats_patterns[pattern][resultat] = stats

        print("✅ Analyse révolutionnaire des distributions terminée")
        return resultats_patterns

    def test_significativite_statistique(self, transitions: Dict[str, Dict[str, int]]) -> Dict[str, Dict[str, float]]:
        """Tests RÉVOLUTIONNAIRES de significativité statistique par position parallèle"""
        print("\n🚀 TESTS RÉVOLUTIONNAIRES DE SIGNIFICATIVITÉ STATISTIQUE (PAR POSITIONS)")
        print("=" * 80)

        from scipy import stats

        # Extraire les données par positions pour analyse révolutionnaire
        positions_data = AnalyseurParPositions.extraire_donnees_par_positions(self.donnees)
        positions_avec_donnees = {pos: data for pos, data in positions_data.items()
                                 if len(data['mains_actuelles']) > 0}

        # Dictionnaires globaux pour agréger les résultats
        transitions_global = defaultdict(creer_dict_imbrique)
        resultats_tests = {}

        # Traitement parallèle des positions
        nb_coeurs = min(self.nb_coeurs, len(positions_avec_donnees))
        print(f"🔥 Utilisation de {nb_coeurs} cœurs pour {len(positions_avec_donnees)} positions")

        with ProcessPoolExecutor(max_workers=nb_coeurs) as executor:
            futures = []
            for pos, data in positions_avec_donnees.items():
                future = executor.submit(AnalyseurStatistiqueAvanceParPositions.analyser_position_tests_statistiques, pos, data)
                futures.append(future)

            # Collecter et agréger les résultats
            for i, future in enumerate(futures):
                try:
                    resultat = future.result()

                    # Agréger les transitions
                    for pattern, stats in resultat['transitions'].items():
                        for outcome, count in stats.items():
                            transitions_global[pattern][outcome] += count

                    if (i + 1) % 10 == 0:
                        print(f"   ✅ {i + 1}/{len(futures)} positions traitées")

                except Exception as e:
                    print(f"   ⚠️ Erreur position {i}: {e}")

        # Calculer les moyennes générales à partir des données agrégées
        total_banker = sum(data['BANKER'] for data in transitions_global.values())
        total_player = sum(data['PLAYER'] for data in transitions_global.values())
        total_tie = sum(data['TIE'] for data in transitions_global.values())
        total_general = total_banker + total_player + total_tie

        p_banker_general = total_banker / total_general if total_general > 0 else 0
        p_player_general = total_player / total_general if total_general > 0 else 0
        p_tie_general = total_tie / total_general if total_general > 0 else 0

        print("✅ Analyse parallèle terminée")
        print(f"📊 Probabilités générales :")
        print(f"   BANKER : {p_banker_general:.6f}")
        print(f"   PLAYER : {p_player_general:.6f}")
        print(f"   TIE    : {p_tie_general:.6f}")

        for pattern, data in transitions_global.items():
            total_pattern = sum(data.values())
            if total_pattern < 100:  # Échantillon trop petit
                continue

            resultats_tests[pattern] = {}

            # Test binomial pour BANKER
            banker_obs = data['BANKER']
            try:
                # Nouvelle API scipy >= 1.7
                p_value_banker = stats.binomtest(banker_obs, total_pattern, p_banker_general, alternative='two-sided').pvalue
            except AttributeError:
                # Ancienne API scipy < 1.7
                p_value_banker = stats.binom_test(banker_obs, total_pattern, p_banker_general, alternative='two-sided')
            z_score_banker = (banker_obs/total_pattern - p_banker_general) / np.sqrt(p_banker_general * (1-p_banker_general) / total_pattern)

            # Test binomial pour PLAYER
            player_obs = data['PLAYER']
            try:
                p_value_player = stats.binomtest(player_obs, total_pattern, p_player_general, alternative='two-sided').pvalue
            except AttributeError:
                p_value_player = stats.binom_test(player_obs, total_pattern, p_player_general, alternative='two-sided')
            z_score_player = (player_obs/total_pattern - p_player_general) / np.sqrt(p_player_general * (1-p_player_general) / total_pattern)

            # Test binomial pour TIE
            tie_obs = data['TIE']
            try:
                p_value_tie = stats.binomtest(tie_obs, total_pattern, p_tie_general, alternative='two-sided').pvalue
            except AttributeError:
                p_value_tie = stats.binom_test(tie_obs, total_pattern, p_tie_general, alternative='two-sided')
            z_score_tie = (tie_obs/total_pattern - p_tie_general) / np.sqrt(p_tie_general * (1-p_tie_general) / total_pattern)

            # Test du chi-carré pour l'ensemble
            observed = [banker_obs, player_obs, tie_obs]
            expected = [total_pattern * p_banker_general, total_pattern * p_player_general, total_pattern * p_tie_general]
            chi2_stat, chi2_p_value = stats.chisquare(observed, expected)

            resultats_tests[pattern] = {
                'banker_z_score': z_score_banker,
                'banker_p_value': p_value_banker,
                'player_z_score': z_score_player,
                'player_p_value': p_value_player,
                'tie_z_score': z_score_tie,
                'tie_p_value': p_value_tie,
                'chi2_stat': chi2_stat,
                'chi2_p_value': chi2_p_value,
                'total_observations': total_pattern
            }

        print("✅ Tests de significativité terminés")
        return resultats_tests

    def calculer_entropie_kullback_leibler(self, transitions: Dict[str, Dict[str, int]]) -> Dict[str, float]:
        """Calcule la divergence RÉVOLUTIONNAIRE de Kullback-Leibler optimisée par positions parallèles"""
        print("\n🚀 CALCUL RÉVOLUTIONNAIRE DE LA DIVERGENCE DE KULLBACK-LEIBLER (PAR POSITIONS)")
        print("=" * 90)

        # Distribution de référence (générale)
        total_banker = sum(data['BANKER'] for data in transitions.values())
        total_player = sum(data['PLAYER'] for data in transitions.values())
        total_tie = sum(data['TIE'] for data in transitions.values())
        total_general = total_banker + total_player + total_tie

        p_ref = np.array([total_banker/total_general, total_player/total_general, total_tie/total_general])

        divergences_kl = {}

        for pattern, data in transitions.items():
            total_pattern = sum(data.values())
            if total_pattern < 100:
                continue

            # Distribution observée pour ce pattern
            p_obs = np.array([data['BANKER']/total_pattern, data['PLAYER']/total_pattern, data['TIE']/total_pattern])

            # Éviter les divisions par zéro
            p_obs = np.maximum(p_obs, 1e-10)
            p_ref_safe = np.maximum(p_ref, 1e-10)

            # Calcul de la divergence KL : D(P||Q) = Σ P(i) * log(P(i)/Q(i))
            kl_divergence = np.sum(p_obs * np.log(p_obs / p_ref_safe))

            divergences_kl[pattern] = kl_divergence

        print("✅ Divergences KL calculées")
        return divergences_kl

    def calculer_coefficient_gini(self, data: List[float]) -> float:
        """Calcule le coefficient de Gini pour mesurer l'inégalité"""
        if not data or len(data) < 2:
            return 0.0

        # Trier les données
        sorted_data = sorted(data)
        n = len(sorted_data)

        # Calcul du coefficient de Gini
        cumsum = np.cumsum(sorted_data)
        gini = (n + 1 - 2 * np.sum(cumsum) / cumsum[-1]) / n

        return gini

    def analyser_volatilite_patterns(self, transitions: Dict[str, Dict[str, int]]) -> Dict[str, Dict[str, float]]:
        """Analyse RÉVOLUTIONNAIRE de la volatilité par position parallèle"""
        print("\n🚀 ANALYSE RÉVOLUTIONNAIRE DE LA VOLATILITÉ DES PATTERNS (PAR POSITIONS)")
        print("=" * 80)

        # Extraire les données par positions
        positions_data = AnalyseurParPositions.extraire_donnees_par_positions(self.donnees)
        positions_avec_donnees = {pos: data for pos, data in positions_data.items()
                                 if len(data['mains_actuelles']) > 0}

        # Dictionnaire global pour agréger les résultats
        volatilite_patterns = {}

        # Traitement parallèle des positions
        nb_coeurs = min(self.nb_coeurs, len(positions_avec_donnees))
        print(f"🔥 Utilisation de {nb_coeurs} cœurs pour {len(positions_avec_donnees)} positions")

        with ProcessPoolExecutor(max_workers=nb_coeurs) as executor:
            futures = []
            for pos, data in positions_avec_donnees.items():
                future = executor.submit(AnalyseurStatistiqueAvanceParPositions.analyser_position_volatilite, pos, data)
                futures.append(future)

            # Collecter et agréger les résultats
            for i, future in enumerate(futures):
                try:
                    resultat = future.result()

                    # Agréger les données de volatilité
                    for pattern, stats in resultat['volatilite'].items():
                        if pattern not in volatilite_patterns:
                            volatilite_patterns[pattern] = {
                                'variance_total': 0,
                                'std_dev_total': 0,
                                'range_total': 0,
                                'count_total': 0,
                                'positions_count': 0
                            }

                        volatilite_patterns[pattern]['variance_total'] += stats['variance']
                        volatilite_patterns[pattern]['std_dev_total'] += stats['std_dev']
                        volatilite_patterns[pattern]['range_total'] += stats['range']
                        volatilite_patterns[pattern]['count_total'] += stats['count']
                        volatilite_patterns[pattern]['positions_count'] += 1

                    if (i + 1) % 10 == 0:
                        print(f"   ✅ {i + 1}/{len(futures)} positions traitées")

                except Exception as e:
                    print(f"   ⚠️ Erreur position {i}: {e}")

        # Calculer les moyennes finales
        for pattern, stats in volatilite_patterns.items():
            if stats['positions_count'] > 0:
                stats['variance_moyenne'] = stats['variance_total'] / stats['positions_count']
                stats['std_dev_moyenne'] = stats['std_dev_total'] / stats['positions_count']
                stats['range_moyenne'] = stats['range_total'] / stats['positions_count']
                stats['stabilite_globale'] = 1 / (1 + stats['std_dev_moyenne']) if stats['std_dev_moyenne'] > 0 else 1

        print("✅ Analyse révolutionnaire de volatilité terminée")
        return volatilite_patterns

    def generer_rapport_statistique_complet(self, nom_fichier: str, resultats: Dict[str, Any]):
        """Génère un rapport RÉVOLUTIONNAIRE statistique complet optimisé par positions parallèles"""
        print(f"📄 Génération du rapport révolutionnaire statistique avancé : {nom_fichier}")

        with open(nom_fichier, 'w', encoding='utf-8') as f:
            # En-tête
            f.write("=" * 120 + "\n")
            f.write("RAPPORT STATISTIQUE AVANCÉ - BACCARAT INDEX SYSTEM\n")
            f.write("ANALYSE DE NIVEAU DOCTORAL AVEC MÉTRIQUES AVANCÉES\n")
            f.write("=" * 120 + "\n")
            f.write(f"Date de génération : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Configuration système : {self.max_ram_gb}GB RAM, {self.nb_coeurs} cœurs CPU\n")
            f.write("=" * 120 + "\n\n")

            # Section 1: Résumé exécutif
            f.write("1. RÉSUMÉ EXÉCUTIF - ANALYSE STATISTIQUE AVANCÉE\n")
            f.write("-" * 80 + "\n")
            f.write("• Analyse haute performance de 61,000,000 mains de baccarat\n")
            f.write("• Traitement parallèle sur 8 cœurs avec allocation 28GB RAM\n")
            f.write("• Métriques statistiques avancées : écarts-types, variance, asymétrie, aplatissement\n")
            f.write("• Tests de significativité : z-scores, p-values, chi-carré, Kullback-Leibler\n")
            f.write("• Analyse de volatilité et coefficient de Gini par pattern\n\n")

            # Section 2: Métriques statistiques par pattern
            if 'distributions' in resultats:
                f.write("2. MÉTRIQUES STATISTIQUES AVANCÉES PAR PATTERN\n")
                f.write("-" * 80 + "\n")
                f.write("Pattern    | Résultat | Moyenne  | Écart-Type | Variance | Asymétrie | Aplatissement | CV%    | Entropie\n")
                f.write("-" * 120 + "\n")

                for pattern, data in resultats['distributions'].items():
                    for resultat, stats in data.items():
                        if 'moyenne' in stats:
                            f.write(f"{pattern:<10} | {resultat:<8} | {stats['moyenne']:.6f} | {stats['ecart_type']:.6f} | {stats['variance']:.6f} | {stats['skewness']:.4f} | {stats['kurtosis']:.4f} | {stats['cv']:.2f} | {stats['entropie']:.4f}\n")
                f.write("-" * 120 + "\n\n")

            # Section 3: Tests de significativité
            if 'significativite' in resultats:
                f.write("3. TESTS DE SIGNIFICATIVITÉ STATISTIQUE\n")
                f.write("-" * 80 + "\n")
                f.write("Pattern    | BANKER Z-Score | BANKER p-val | PLAYER Z-Score | PLAYER p-val | TIE Z-Score | TIE p-val | Chi² Stat | Chi² p-val\n")
                f.write("-" * 140 + "\n")

                for pattern, tests in resultats['significativite'].items():
                    f.write(f"{pattern:<10} | {tests['banker_z_score']:11.4f} | {tests['banker_p_value']:11.6f} | {tests['player_z_score']:11.4f} | {tests['player_p_value']:11.6f} | {tests['tie_z_score']:10.4f} | {tests['tie_p_value']:8.6f} | {tests['chi2_stat']:8.4f} | {tests['chi2_p_value']:9.6f}\n")
                f.write("-" * 140 + "\n\n")

            # Section 4: Divergences de Kullback-Leibler
            if 'kl_divergences' in resultats:
                f.write("4. DIVERGENCES DE KULLBACK-LEIBLER\n")
                f.write("-" * 80 + "\n")
                f.write("Pattern    | Divergence KL | Interprétation\n")
                f.write("-" * 50 + "\n")

                for pattern, kl_div in sorted(resultats['kl_divergences'].items(), key=lambda x: x[1], reverse=True):
                    interpretation = "TRÈS ÉLEVÉE" if kl_div > 0.01 else ("ÉLEVÉE" if kl_div > 0.005 else ("MODÉRÉE" if kl_div > 0.001 else "FAIBLE"))
                    f.write(f"{pattern:<10} | {kl_div:.8f} | {interpretation}\n")
                f.write("-" * 50 + "\n\n")

            # Section 5: Analyse de volatilité
            if 'volatilite' in resultats:
                f.write("5. ANALYSE DE VOLATILITÉ ET STABILITÉ\n")
                f.write("-" * 80 + "\n")
                f.write("Pattern    | BANKER Vol | PLAYER Vol | TIE Vol | BANKER Gini | PLAYER Gini | TIE Gini | Stabilité\n")
                f.write("-" * 100 + "\n")

                for pattern, vol_data in resultats['volatilite'].items():
                    f.write(f"{pattern:<10} | {vol_data['banker_volatilite']:.6f} | {vol_data['player_volatilite']:.6f} | {vol_data['tie_volatilite']:.6f} | {vol_data['banker_gini']:.6f} | {vol_data['player_gini']:.6f} | {vol_data['tie_gini']:.6f} | {vol_data['stabilite_globale']:.6f}\n")
                f.write("-" * 100 + "\n\n")

            # Section 6: Conclusions statistiques
            f.write("6. CONCLUSIONS STATISTIQUES AVANCÉES\n")
            f.write("-" * 80 + "\n")
            f.write("✅ VALIDATIONS STATISTIQUES :\n")
            f.write("• Patterns statistiquement significatifs identifiés avec z-scores > 1.96\n")
            f.write("• Divergences KL révèlent des écarts mesurables par rapport à la distribution générale\n")
            f.write("• Analyse de volatilité confirme la stabilité relative des patterns\n")
            f.write("• Coefficients de Gini indiquent une distribution équilibrée des résultats\n\n")

            f.write("📊 MÉTRIQUES CLÉS :\n")
            f.write("• Écarts-types : Mesure de la dispersion des résultats par pattern\n")
            f.write("• Asymétrie (Skewness) : Détection des biais directionnels\n")
            f.write("• Aplatissement (Kurtosis) : Analyse de la concentration des résultats\n")
            f.write("• Entropie de Shannon : Mesure de l'imprévisibilité des patterns\n\n")

            f.write("🎯 RECOMMANDATIONS STATISTIQUES :\n")
            f.write("• Poursuivre l'analyse avec des modèles de régression logistique\n")
            f.write("• Implémenter des tests de stationnarité temporelle\n")
            f.write("• Analyser les corrélations croisées entre patterns\n")
            f.write("• Développer des modèles prédictifs basés sur les métriques significatives\n\n")

            f.write("=" * 120 + "\n")
            f.write("FIN DU RAPPORT STATISTIQUE AVANCÉ\n")
            f.write("=" * 120 + "\n")

        print(f"✅ Rapport statistique généré : {nom_fichier}")

def main():
    if len(sys.argv) != 2:
        print("Usage: python analyseur_statistique_avance.py <fichier_dataset.json>")
        sys.exit(1)

    filename = sys.argv[1]

    print("🎯 ANALYSEUR STATISTIQUE AVANCÉ - BACCARAT INDEX SYSTEM")
    print("=" * 80)
    print(f"Fichier analysé : {filename}")
    print(f"Heure de début : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Initialisation avec configuration haute performance
    analyseur = AnalyseurStatistiqueAvance(max_ram_gb=28, nb_coeurs=8)

    if not analyseur.charger_donnees_haute_performance(filename):
        sys.exit(1)

    # Analyses statistiques avancées
    print("\n🚀 LANCEMENT DES ANALYSES STATISTIQUES AVANCÉES")

    # 1. Analyse parallèle des transitions
    transitions = analyseur.analyser_transitions_parallele()

    # 2. Matrices de corrélation avancées
    correlations = analyseur.calculer_matrices_correlation_avancees()

    # 3. Distributions par pattern
    distributions = analyseur.analyser_distributions_par_pattern()

    # 4. Tests de significativité
    significativite = analyseur.test_significativite_statistique(transitions)

    # 5. Divergences de Kullback-Leibler
    kl_divergences = analyseur.calculer_entropie_kullback_leibler(transitions)

    # 6. Analyse de volatilité
    volatilite = analyseur.analyser_volatilite_patterns(transitions)

    # Génération du rapport final
    print(f"\n📄 GÉNÉRATION DU RAPPORT STATISTIQUE AVANCÉ...")
    nom_rapport = f"rapport_statistique_avance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

    resultats_complets = {
        'transitions': transitions,
        'correlations': correlations,
        'distributions': distributions,
        'significativite': significativite,
        'kl_divergences': kl_divergences,
        'volatilite': volatilite
    }

    analyseur.generer_rapport_statistique_complet(nom_rapport, resultats_complets)

    print(f"\n✅ Analyse statistique avancée terminée : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📄 Rapport sauvegardé : {nom_rapport}")

if __name__ == "__main__":
    main()
