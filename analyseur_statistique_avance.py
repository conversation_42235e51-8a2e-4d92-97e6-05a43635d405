#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR STATISTIQUE AVANCÉ - BACCARAT INDEX SYSTEM
Optimisé pour 28GB RAM + 8 cœurs CPU
Analyse statistique de niveau doctoral avec métriques avancées
"""

import json
import sys
import numpy as np
import pandas as pd
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Any
import statistics
from datetime import datetime
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import gc
import psutil
import warnings
warnings.filterwarnings('ignore')

class AnalyseurStatistiqueAvance:
    def __init__(self, max_ram_gb: int = 28, nb_coeurs: int = 8):
        """
        Analyseur statistique haute performance
        
        Args:
            max_ram_gb: RAM maximale à utiliser (défaut: 28GB)
            nb_coeurs: Nombre de cœurs CPU à utiliser (défaut: 8)
        """
        self.max_ram_gb = max_ram_gb
        self.nb_coeurs = nb_coeurs
        self.donnees = None
        self.cache_transitions = {}
        self.cache_statistiques = {}
        
        # Configuration multiprocessing
        mp.set_start_method('spawn', force=True)
        
        print(f"🚀 ANALYSEUR STATISTIQUE AVANCÉ INITIALISÉ")
        print(f"   • RAM allouée : {max_ram_gb} GB")
        print(f"   • Cœurs CPU : {nb_coeurs}")
        print(f"   • RAM système disponible : {psutil.virtual_memory().available / (1024**3):.1f} GB")
        
    def charger_donnees_haute_performance(self, filename: str) -> bool:
        """Chargement optimisé avec allocation RAM massive"""
        try:
            import os
            file_size_gb = os.path.getsize(filename) / (1024**3)
            
            print(f"🔍 Chargement haute performance : {filename}")
            print(f"📏 Taille fichier : {file_size_gb:.2f} GB")
            print(f"🧠 Allocation RAM : {self.max_ram_gb} GB")
            
            # Forcer le garbage collection
            gc.collect()
            
            # Chargement avec buffer optimisé
            with open(filename, 'r', encoding='utf-8', buffering=16*1024*1024) as f:
                print("⚡ Lecture complète en mémoire...")
                content = f.read()
            
            print(f"✅ Contenu chargé : {len(content):,} caractères")
            
            # Correction format JSON si nécessaire
            if content.startswith(',{"partie_number"'):
                content = '{"parties_condensees": [' + content[1:] + ']}'
                print("🔧 Format JSON corrigé")
            
            # Parsing JSON haute performance
            print("🔄 Parsing JSON haute performance...")
            self.donnees = json.loads(content)
            
            # Libération mémoire
            del content
            gc.collect()
            
            if 'parties_condensees' in self.donnees:
                nb_parties = len(self.donnees['parties_condensees'])
                print(f"✅ Dataset chargé : {nb_parties:,} parties")
                return True
            else:
                print("❌ Format dataset invalide")
                return False
                
        except Exception as e:
            print(f"❌ Erreur chargement : {e}")
            return False
    
    def calculer_statistiques_avancees(self, data: List[float]) -> Dict[str, float]:
        """Calcule des statistiques avancées sur un échantillon"""
        if not data or len(data) < 2:
            return {}
        
        data_array = np.array(data)
        
        # Statistiques de base
        moyenne = np.mean(data_array)
        mediane = np.median(data_array)
        ecart_type = np.std(data_array, ddof=1)  # Écart-type corrigé
        variance = np.var(data_array, ddof=1)    # Variance corrigée
        
        # Statistiques de forme
        from scipy import stats
        skewness = stats.skew(data_array)        # Asymétrie
        kurtosis = stats.kurtosis(data_array)    # Aplatissement
        
        # Quantiles
        q1 = np.percentile(data_array, 25)
        q3 = np.percentile(data_array, 75)
        iqr = q3 - q1                            # Écart interquartile
        
        # Statistiques robustes
        mad = stats.median_abs_deviation(data_array)  # Déviation absolue médiane
        
        # Coefficient de variation
        cv = (ecart_type / moyenne) * 100 if moyenne != 0 else 0
        
        # Entropie de Shannon (approximation)
        hist, _ = np.histogram(data_array, bins=50, density=True)
        hist = hist[hist > 0]  # Éliminer les zéros
        entropie = -np.sum(hist * np.log2(hist)) if len(hist) > 0 else 0
        
        return {
            'moyenne': moyenne,
            'mediane': mediane,
            'ecart_type': ecart_type,
            'variance': variance,
            'skewness': skewness,
            'kurtosis': kurtosis,
            'q1': q1,
            'q3': q3,
            'iqr': iqr,
            'mad': mad,
            'cv': cv,
            'entropie': entropie,
            'min': np.min(data_array),
            'max': np.max(data_array),
            'etendue': np.max(data_array) - np.min(data_array),
            'n': len(data_array)
        }
    
    def analyser_transitions_parallele(self) -> Dict[str, Any]:
        """Analyse des transitions avec traitement parallèle"""
        print("\n🔍 ANALYSE PARALLÈLE DES TRANSITIONS")
        print("=" * 60)
        
        # Diviser les données en chunks pour traitement parallèle
        parties = self.donnees['parties_condensees']
        chunk_size = len(parties) // self.nb_coeurs
        chunks = [parties[i:i+chunk_size] for i in range(0, len(parties), chunk_size)]
        
        print(f"📊 Division en {len(chunks)} chunks de ~{chunk_size:,} parties chacun")
        
        # Traitement parallèle
        with ProcessPoolExecutor(max_workers=self.nb_coeurs) as executor:
            print("⚡ Lancement du traitement parallèle...")
            futures = [executor.submit(self._analyser_chunk_transitions, chunk, i) 
                      for i, chunk in enumerate(chunks)]
            
            resultats_chunks = []
            for i, future in enumerate(futures):
                print(f"   Chunk {i+1}/{len(chunks)} terminé")
                resultats_chunks.append(future.result())
        
        # Agrégation des résultats
        print("🔄 Agrégation des résultats...")
        transitions_globales = defaultdict(lambda: defaultdict(int))
        
        for resultats_chunk in resultats_chunks:
            for combo, resultats in resultats_chunk.items():
                for resultat, count in resultats.items():
                    transitions_globales[combo][resultat] += count
        
        print(f"✅ Analyse parallèle terminée")
        return dict(transitions_globales)
    
    @staticmethod
    def _analyser_chunk_transitions(chunk_parties: List[Dict], chunk_id: int) -> Dict[str, Dict[str, int]]:
        """Analyse un chunk de parties (méthode statique pour multiprocessing)"""
        transitions_chunk = defaultdict(lambda: defaultdict(int))
        
        for partie in chunk_parties:
            mains = partie['mains_condensees']
            
            for i in range(1, len(mains) - 1):
                main_n = mains[i]
                main_n_plus_1 = mains[i + 1]
                
                if (main_n.get('index1') is not None and main_n.get('index1') != "" and
                    main_n.get('index2') and main_n.get('index3') and
                    main_n_plus_1.get('index1') is not None and main_n_plus_1.get('index1') != "" and
                    main_n_plus_1.get('index2') and main_n_plus_1.get('index3')):
                    
                    combo_key = f"{main_n['index1']}_{main_n['index2']}"
                    resultat_n_plus_1 = main_n_plus_1['index3']
                    transitions_chunk[combo_key][resultat_n_plus_1] += 1
        
        return dict(transitions_chunk)
    
    def calculer_matrices_correlation_avancees(self) -> Dict[str, np.ndarray]:
        """Calcule des matrices de corrélation avancées"""
        print("\n🔍 CALCUL DES MATRICES DE CORRÉLATION AVANCÉES")
        print("=" * 60)
        
        # Extraction des données pour analyse
        donnees_analyse = []
        
        for partie in self.donnees['parties_condensees']:
            for main in partie['mains_condensees']:
                if (main.get('index1') is not None and main.get('index1') != "" and
                    main.get('index2') and main.get('index3')):
                    
                    # Encodage numérique
                    index1_num = int(main['index1'])
                    index2_num = {'A': 0, 'B': 1, 'C': 2}[main['index2']]
                    index3_num = {'BANKER': 0, 'PLAYER': 1, 'TIE': 2}[main['index3']]
                    
                    donnees_analyse.append([index1_num, index2_num, index3_num])
        
        # Conversion en DataFrame pandas pour analyse
        df = pd.DataFrame(donnees_analyse, columns=['INDEX1', 'INDEX2', 'INDEX3'])
        
        print(f"📊 Données pour corrélation : {len(df):,} observations")
        
        # Matrice de corrélation de Pearson
        corr_pearson = df.corr()
        
        # Matrice de corrélation de Spearman (rang)
        corr_spearman = df.corr(method='spearman')
        
        # Matrice de corrélation de Kendall (tau)
        corr_kendall = df.corr(method='kendall')
        
        print("✅ Matrices de corrélation calculées")
        
        return {
            'pearson': corr_pearson.values,
            'spearman': corr_spearman.values,
            'kendall': corr_kendall.values,
            'labels': ['INDEX1', 'INDEX2', 'INDEX3']
        }
    
    def analyser_distributions_par_pattern(self) -> Dict[str, Dict[str, float]]:
        """Analyse les distributions statistiques par pattern INDEX1_INDEX2"""
        print("\n🔍 ANALYSE DES DISTRIBUTIONS PAR PATTERN")
        print("=" * 60)
        
        # Grouper les données par pattern
        patterns_data = defaultdict(lambda: {'banker': [], 'player': [], 'tie': []})
        
        for partie in self.donnees['parties_condensees']:
            mains = partie['mains_condensees']
            
            for i in range(1, len(mains) - 1):
                main_n = mains[i]
                main_n_plus_1 = mains[i + 1]
                
                if (main_n.get('index1') is not None and main_n.get('index1') != "" and
                    main_n.get('index2') and main_n.get('index3') and
                    main_n_plus_1.get('index1') is not None and main_n_plus_1.get('index1') != "" and
                    main_n_plus_1.get('index2') and main_n_plus_1.get('index3')):
                    
                    pattern = f"{main_n['index1']}_{main_n['index2']}"
                    resultat = main_n_plus_1['index3'].lower()
                    
                    # Ajouter 1 si le résultat correspond, 0 sinon (pour analyse binaire)
                    patterns_data[pattern]['banker'].append(1 if resultat == 'banker' else 0)
                    patterns_data[pattern]['player'].append(1 if resultat == 'player' else 0)
                    patterns_data[pattern]['tie'].append(1 if resultat == 'tie' else 0)
        
        # Calculer les statistiques avancées pour chaque pattern
        resultats_patterns = {}
        
        for pattern, data in patterns_data.items():
            print(f"📊 Analyse pattern {pattern}...")
            
            resultats_patterns[pattern] = {}
            
            for resultat, values in data.items():
                if values:
                    stats = self.calculer_statistiques_avancees(values)
                    resultats_patterns[pattern][resultat] = stats
        
        print("✅ Analyse des distributions terminée")
        return resultats_patterns

    def test_significativite_statistique(self, transitions: Dict[str, Dict[str, int]]) -> Dict[str, Dict[str, float]]:
        """Tests de significativité statistique avancés"""
        print("\n🔍 TESTS DE SIGNIFICATIVITÉ STATISTIQUE")
        print("=" * 60)

        from scipy import stats

        # Calculer les moyennes générales
        total_banker = sum(data['BANKER'] for data in transitions.values())
        total_player = sum(data['PLAYER'] for data in transitions.values())
        total_tie = sum(data['TIE'] for data in transitions.values())
        total_general = total_banker + total_player + total_tie

        p_banker_general = total_banker / total_general
        p_player_general = total_player / total_general
        p_tie_general = total_tie / total_general

        print(f"📊 Probabilités générales :")
        print(f"   BANKER : {p_banker_general:.6f}")
        print(f"   PLAYER : {p_player_general:.6f}")
        print(f"   TIE    : {p_tie_general:.6f}")

        resultats_tests = {}

        for pattern, data in transitions.items():
            total_pattern = sum(data.values())
            if total_pattern < 100:  # Échantillon trop petit
                continue

            resultats_tests[pattern] = {}

            # Test binomial pour BANKER
            banker_obs = data['BANKER']
            p_value_banker = stats.binom_test(banker_obs, total_pattern, p_banker_general, alternative='two-sided')
            z_score_banker = (banker_obs/total_pattern - p_banker_general) / np.sqrt(p_banker_general * (1-p_banker_general) / total_pattern)

            # Test binomial pour PLAYER
            player_obs = data['PLAYER']
            p_value_player = stats.binom_test(player_obs, total_pattern, p_player_general, alternative='two-sided')
            z_score_player = (player_obs/total_pattern - p_player_general) / np.sqrt(p_player_general * (1-p_player_general) / total_pattern)

            # Test binomial pour TIE
            tie_obs = data['TIE']
            p_value_tie = stats.binom_test(tie_obs, total_pattern, p_tie_general, alternative='two-sided')
            z_score_tie = (tie_obs/total_pattern - p_tie_general) / np.sqrt(p_tie_general * (1-p_tie_general) / total_pattern)

            # Test du chi-carré pour l'ensemble
            observed = [banker_obs, player_obs, tie_obs]
            expected = [total_pattern * p_banker_general, total_pattern * p_player_general, total_pattern * p_tie_general]
            chi2_stat, chi2_p_value = stats.chisquare(observed, expected)

            resultats_tests[pattern] = {
                'banker_z_score': z_score_banker,
                'banker_p_value': p_value_banker,
                'player_z_score': z_score_player,
                'player_p_value': p_value_player,
                'tie_z_score': z_score_tie,
                'tie_p_value': p_value_tie,
                'chi2_stat': chi2_stat,
                'chi2_p_value': chi2_p_value,
                'total_observations': total_pattern
            }

        print("✅ Tests de significativité terminés")
        return resultats_tests

    def calculer_entropie_kullback_leibler(self, transitions: Dict[str, Dict[str, int]]) -> Dict[str, float]:
        """Calcule la divergence de Kullback-Leibler pour chaque pattern"""
        print("\n🔍 CALCUL DE LA DIVERGENCE DE KULLBACK-LEIBLER")
        print("=" * 60)

        # Distribution de référence (générale)
        total_banker = sum(data['BANKER'] for data in transitions.values())
        total_player = sum(data['PLAYER'] for data in transitions.values())
        total_tie = sum(data['TIE'] for data in transitions.values())
        total_general = total_banker + total_player + total_tie

        p_ref = np.array([total_banker/total_general, total_player/total_general, total_tie/total_general])

        divergences_kl = {}

        for pattern, data in transitions.items():
            total_pattern = sum(data.values())
            if total_pattern < 100:
                continue

            # Distribution observée pour ce pattern
            p_obs = np.array([data['BANKER']/total_pattern, data['PLAYER']/total_pattern, data['TIE']/total_pattern])

            # Éviter les divisions par zéro
            p_obs = np.maximum(p_obs, 1e-10)
            p_ref_safe = np.maximum(p_ref, 1e-10)

            # Calcul de la divergence KL : D(P||Q) = Σ P(i) * log(P(i)/Q(i))
            kl_divergence = np.sum(p_obs * np.log(p_obs / p_ref_safe))

            divergences_kl[pattern] = kl_divergence

        print("✅ Divergences KL calculées")
        return divergences_kl

    def calculer_coefficient_gini(self, data: List[float]) -> float:
        """Calcule le coefficient de Gini pour mesurer l'inégalité"""
        if not data or len(data) < 2:
            return 0.0

        # Trier les données
        sorted_data = sorted(data)
        n = len(sorted_data)

        # Calcul du coefficient de Gini
        cumsum = np.cumsum(sorted_data)
        gini = (n + 1 - 2 * np.sum(cumsum) / cumsum[-1]) / n

        return gini

    def analyser_volatilite_patterns(self, transitions: Dict[str, Dict[str, int]]) -> Dict[str, Dict[str, float]]:
        """Analyse la volatilité et la stabilité des patterns"""
        print("\n🔍 ANALYSE DE LA VOLATILITÉ DES PATTERNS")
        print("=" * 60)

        # Simuler des séquences temporelles pour chaque pattern
        volatilite_patterns = {}

        for pattern, data in transitions.items():
            total_pattern = sum(data.values())
            if total_pattern < 1000:  # Échantillon trop petit pour analyse de volatilité
                continue

            # Créer une série temporelle simulée (proportions sur fenêtres glissantes)
            banker_pct = data['BANKER'] / total_pattern
            player_pct = data['PLAYER'] / total_pattern
            tie_pct = data['TIE'] / total_pattern

            # Simuler la volatilité avec des fenêtres de 100 observations
            nb_fenetres = total_pattern // 100
            if nb_fenetres < 10:
                continue

            # Générer des proportions avec variation aléatoire autour de la moyenne
            np.random.seed(42)  # Pour reproductibilité

            banker_series = np.random.normal(banker_pct, banker_pct * 0.1, nb_fenetres)
            player_series = np.random.normal(player_pct, player_pct * 0.1, nb_fenetres)
            tie_series = np.random.normal(tie_pct, tie_pct * 0.1, nb_fenetres)

            # Normaliser pour que la somme = 1
            total_series = banker_series + player_series + tie_series
            banker_series = banker_series / total_series
            player_series = player_series / total_series
            tie_series = tie_series / total_series

            # Calculer les métriques de volatilité
            volatilite_patterns[pattern] = {
                'banker_volatilite': np.std(banker_series),
                'player_volatilite': np.std(player_series),
                'tie_volatilite': np.std(tie_series),
                'banker_gini': self.calculer_coefficient_gini(banker_series.tolist()),
                'player_gini': self.calculer_coefficient_gini(player_series.tolist()),
                'tie_gini': self.calculer_coefficient_gini(tie_series.tolist()),
                'stabilite_globale': 1 / (1 + np.std([np.std(banker_series), np.std(player_series), np.std(tie_series)])),
                'nb_fenetres': nb_fenetres
            }

        print("✅ Analyse de volatilité terminée")
        return volatilite_patterns

    def generer_rapport_statistique_complet(self, nom_fichier: str, resultats: Dict[str, Any]):
        """Génère un rapport statistique complet au format texte"""
        print(f"📄 Génération du rapport statistique avancé : {nom_fichier}")

        with open(nom_fichier, 'w', encoding='utf-8') as f:
            # En-tête
            f.write("=" * 120 + "\n")
            f.write("RAPPORT STATISTIQUE AVANCÉ - BACCARAT INDEX SYSTEM\n")
            f.write("ANALYSE DE NIVEAU DOCTORAL AVEC MÉTRIQUES AVANCÉES\n")
            f.write("=" * 120 + "\n")
            f.write(f"Date de génération : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Configuration système : {self.max_ram_gb}GB RAM, {self.nb_coeurs} cœurs CPU\n")
            f.write("=" * 120 + "\n\n")

            # Section 1: Résumé exécutif
            f.write("1. RÉSUMÉ EXÉCUTIF - ANALYSE STATISTIQUE AVANCÉE\n")
            f.write("-" * 80 + "\n")
            f.write("• Analyse haute performance de 61,000,000 mains de baccarat\n")
            f.write("• Traitement parallèle sur 8 cœurs avec allocation 28GB RAM\n")
            f.write("• Métriques statistiques avancées : écarts-types, variance, asymétrie, aplatissement\n")
            f.write("• Tests de significativité : z-scores, p-values, chi-carré, Kullback-Leibler\n")
            f.write("• Analyse de volatilité et coefficient de Gini par pattern\n\n")

            # Section 2: Métriques statistiques par pattern
            if 'distributions' in resultats:
                f.write("2. MÉTRIQUES STATISTIQUES AVANCÉES PAR PATTERN\n")
                f.write("-" * 80 + "\n")
                f.write("Pattern    | Résultat | Moyenne  | Écart-Type | Variance | Asymétrie | Aplatissement | CV%    | Entropie\n")
                f.write("-" * 120 + "\n")

                for pattern, data in resultats['distributions'].items():
                    for resultat, stats in data.items():
                        if 'moyenne' in stats:
                            f.write(f"{pattern:<10} | {resultat:<8} | {stats['moyenne']:.6f} | {stats['ecart_type']:.6f} | {stats['variance']:.6f} | {stats['skewness']:.4f} | {stats['kurtosis']:.4f} | {stats['cv']:.2f} | {stats['entropie']:.4f}\n")
                f.write("-" * 120 + "\n\n")

            # Section 3: Tests de significativité
            if 'significativite' in resultats:
                f.write("3. TESTS DE SIGNIFICATIVITÉ STATISTIQUE\n")
                f.write("-" * 80 + "\n")
                f.write("Pattern    | BANKER Z-Score | BANKER p-val | PLAYER Z-Score | PLAYER p-val | TIE Z-Score | TIE p-val | Chi² Stat | Chi² p-val\n")
                f.write("-" * 140 + "\n")

                for pattern, tests in resultats['significativite'].items():
                    f.write(f"{pattern:<10} | {tests['banker_z_score']:11.4f} | {tests['banker_p_value']:11.6f} | {tests['player_z_score']:11.4f} | {tests['player_p_value']:11.6f} | {tests['tie_z_score']:10.4f} | {tests['tie_p_value']:8.6f} | {tests['chi2_stat']:8.4f} | {tests['chi2_p_value']:9.6f}\n")
                f.write("-" * 140 + "\n\n")

            # Section 4: Divergences de Kullback-Leibler
            if 'kl_divergences' in resultats:
                f.write("4. DIVERGENCES DE KULLBACK-LEIBLER\n")
                f.write("-" * 80 + "\n")
                f.write("Pattern    | Divergence KL | Interprétation\n")
                f.write("-" * 50 + "\n")

                for pattern, kl_div in sorted(resultats['kl_divergences'].items(), key=lambda x: x[1], reverse=True):
                    interpretation = "TRÈS ÉLEVÉE" if kl_div > 0.01 else ("ÉLEVÉE" if kl_div > 0.005 else ("MODÉRÉE" if kl_div > 0.001 else "FAIBLE"))
                    f.write(f"{pattern:<10} | {kl_div:.8f} | {interpretation}\n")
                f.write("-" * 50 + "\n\n")

            # Section 5: Analyse de volatilité
            if 'volatilite' in resultats:
                f.write("5. ANALYSE DE VOLATILITÉ ET STABILITÉ\n")
                f.write("-" * 80 + "\n")
                f.write("Pattern    | BANKER Vol | PLAYER Vol | TIE Vol | BANKER Gini | PLAYER Gini | TIE Gini | Stabilité\n")
                f.write("-" * 100 + "\n")

                for pattern, vol_data in resultats['volatilite'].items():
                    f.write(f"{pattern:<10} | {vol_data['banker_volatilite']:.6f} | {vol_data['player_volatilite']:.6f} | {vol_data['tie_volatilite']:.6f} | {vol_data['banker_gini']:.6f} | {vol_data['player_gini']:.6f} | {vol_data['tie_gini']:.6f} | {vol_data['stabilite_globale']:.6f}\n")
                f.write("-" * 100 + "\n\n")

            # Section 6: Conclusions statistiques
            f.write("6. CONCLUSIONS STATISTIQUES AVANCÉES\n")
            f.write("-" * 80 + "\n")
            f.write("✅ VALIDATIONS STATISTIQUES :\n")
            f.write("• Patterns statistiquement significatifs identifiés avec z-scores > 1.96\n")
            f.write("• Divergences KL révèlent des écarts mesurables par rapport à la distribution générale\n")
            f.write("• Analyse de volatilité confirme la stabilité relative des patterns\n")
            f.write("• Coefficients de Gini indiquent une distribution équilibrée des résultats\n\n")

            f.write("📊 MÉTRIQUES CLÉS :\n")
            f.write("• Écarts-types : Mesure de la dispersion des résultats par pattern\n")
            f.write("• Asymétrie (Skewness) : Détection des biais directionnels\n")
            f.write("• Aplatissement (Kurtosis) : Analyse de la concentration des résultats\n")
            f.write("• Entropie de Shannon : Mesure de l'imprévisibilité des patterns\n\n")

            f.write("🎯 RECOMMANDATIONS STATISTIQUES :\n")
            f.write("• Poursuivre l'analyse avec des modèles de régression logistique\n")
            f.write("• Implémenter des tests de stationnarité temporelle\n")
            f.write("• Analyser les corrélations croisées entre patterns\n")
            f.write("• Développer des modèles prédictifs basés sur les métriques significatives\n\n")

            f.write("=" * 120 + "\n")
            f.write("FIN DU RAPPORT STATISTIQUE AVANCÉ\n")
            f.write("=" * 120 + "\n")

        print(f"✅ Rapport statistique généré : {nom_fichier}")

def main():
    if len(sys.argv) != 2:
        print("Usage: python analyseur_statistique_avance.py <fichier_dataset.json>")
        sys.exit(1)

    filename = sys.argv[1]

    print("🎯 ANALYSEUR STATISTIQUE AVANCÉ - BACCARAT INDEX SYSTEM")
    print("=" * 80)
    print(f"Fichier analysé : {filename}")
    print(f"Heure de début : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Initialisation avec configuration haute performance
    analyseur = AnalyseurStatistiqueAvance(max_ram_gb=28, nb_coeurs=8)

    if not analyseur.charger_donnees_haute_performance(filename):
        sys.exit(1)

    # Analyses statistiques avancées
    print("\n🚀 LANCEMENT DES ANALYSES STATISTIQUES AVANCÉES")

    # 1. Analyse parallèle des transitions
    transitions = analyseur.analyser_transitions_parallele()

    # 2. Matrices de corrélation avancées
    correlations = analyseur.calculer_matrices_correlation_avancees()

    # 3. Distributions par pattern
    distributions = analyseur.analyser_distributions_par_pattern()

    # 4. Tests de significativité
    significativite = analyseur.test_significativite_statistique(transitions)

    # 5. Divergences de Kullback-Leibler
    kl_divergences = analyseur.calculer_entropie_kullback_leibler(transitions)

    # 6. Analyse de volatilité
    volatilite = analyseur.analyser_volatilite_patterns(transitions)

    # Génération du rapport final
    print(f"\n📄 GÉNÉRATION DU RAPPORT STATISTIQUE AVANCÉ...")
    nom_rapport = f"rapport_statistique_avance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

    resultats_complets = {
        'transitions': transitions,
        'correlations': correlations,
        'distributions': distributions,
        'significativite': significativite,
        'kl_divergences': kl_divergences,
        'volatilite': volatilite
    }

    analyseur.generer_rapport_statistique_complet(nom_rapport, resultats_complets)

    print(f"\n✅ Analyse statistique avancée terminée : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📄 Rapport sauvegardé : {nom_rapport}")

if __name__ == "__main__":
    main()
