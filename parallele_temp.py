#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR PARALLÈLE AUTONOME - BACCARAT INDEX SYSTEM
====================================================

Module autonome contenant toutes les méthodes nécessaires pour l'analyse
des transitions INDEX et la génération du rapport complet.

Auteur: Système d'analyse baccarat INDEX
Date: 2025-07-05
"""

# ============================================================================
# SECTION 1 : IMPORTS ET CONFIGURATION GLOBALE
# ============================================================================

import json
import sys
import os
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Any, Optional
import statistics
from datetime import datetime
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import multiprocessing as mp
import gc
import warnings
warnings.filterwarnings('ignore')

# Détection de l'environnement pour gérer l'encodage Windows
SUBPROCESS_MODE = not sys.stdout.isatty() or (hasattr(sys.stdout, 'encoding') and sys.stdout.encoding in ['cp1252', 'cp850'])

# Imports pour analyses statistiques avancées
try:
    import numpy as np
    import pandas as pd
    from scipy import stats
    import psutil
    ADVANCED_STATS_AVAILABLE = True
    print("✅ Modules statistiques avancés disponibles")
except ImportError as e:
    ADVANCED_STATS_AVAILABLE = False
    print(f"⚠️ Modules statistiques avancés non disponibles : {e}")
    print("💡 Pour les analyses avancées, installez : pip install numpy pandas scipy psutil")

# Configuration globale
TRANSITIONS_INDEX_AVAILABLE = True

# ============================================================================
# SECTION 2 : FONCTIONS UTILITAIRES GLOBALES
# ============================================================================

def safe_print(text):
    """Affichage sécurisé compatible subprocess Windows"""
    try:
        if SUBPROCESS_MODE:
            # Mode subprocess : affichage simple sans emojis
            clean_text = text.replace('✅', '[OK]').replace('❌', '[ERR]').replace('🔍', '[INFO]')
            clean_text = clean_text.replace('📊', '[STAT]').replace('🚀', '[START]').replace('💡', '[TIP]')
            clean_text = clean_text.replace('⚠️', '[WARN]').replace('🎯', '[TARGET]').replace('📄', '[FILE]')
            clean_text = clean_text.replace('🔥', '[FIRE]').replace('📈', '[PROG]').replace('🔗', '[LINK]')
            print(clean_text)
        else:
            # Mode normal : affichage avec emojis
            print(text)

        # Force le flush pour subprocess
        sys.stdout.flush()
    except Exception:
        # Fallback ultime
        print(str(text).encode('ascii', 'ignore').decode('ascii'))

def creer_dict_imbrique():
    """Crée un dictionnaire imbriqué compatible avec multiprocessing"""
    return defaultdict(int)

def creer_dict_patterns():
    """Crée un dictionnaire de patterns compatible avec multiprocessing"""
    return {'banker': [], 'player': [], 'tie': []}

# ============================================================================
# SECTION 3 : CLASSE ANALYSEUR TRANSITIONS INDEX
# ============================================================================

class AnalyseurTransitionsIndex:
    """Analyseur des transitions entre INDEX pour chaque position de main"""
    
    def __init__(self):
        """Initialisation de l'analyseur"""
        self.donnees = None
        
        # Mappings INDEX
        self.index6_mapping = {
            '0_A': 'M', '0_B': 'N', '0_C': 'O',
            '1_A': 'S', '1_B': 'T', '1_C': 'U'
        }
        
        # INDEX5 : toutes les combinaisons INDEX1_INDEX2_INDEX3
        self.index5_values = [
            '0_A_BANKER', '0_B_BANKER', '0_C_BANKER', '1_A_BANKER', '1_B_BANKER', '1_C_BANKER',
            '0_A_PLAYER', '0_B_PLAYER', '0_C_PLAYER', '1_A_PLAYER', '1_B_PLAYER', '1_C_PLAYER',
            '0_A_TIE', '0_B_TIE', '0_C_TIE', '1_A_TIE', '1_B_TIE', '1_C_TIE'
        ]
        
        # INDEX7 : toutes les combinaisons INDEX6_INDEX3
        self.index7_values = [
            'M_BANKER', 'N_BANKER', 'O_BANKER', 'S_BANKER', 'T_BANKER', 'U_BANKER',
            'M_PLAYER', 'N_PLAYER', 'O_PLAYER', 'S_PLAYER', 'T_PLAYER', 'U_PLAYER',
            'M_TIE', 'N_TIE', 'O_TIE', 'S_TIE', 'T_TIE', 'U_TIE'
        ]
        
        # Résultats des analyses
        self.transitions_index5 = {}  # main_n -> {index5_n -> {index5_n+1 -> count}}
        self.transitions_index1_index2 = {}  # main_n -> {index1_index2_n -> {index5_n+1, index3_n+1, index7_n+1 -> count}}

    def charger_donnees(self, donnees: Dict) -> bool:
        """Charge les données depuis un dictionnaire"""
        try:
            self.donnees = donnees
            print(f"✅ Données chargées pour l'analyse des transitions INDEX")
            return True
        except Exception as e:
            print(f"❌ Erreur lors du chargement des données : {e}")
            return False

    def construire_index5(self, main: Dict) -> str:
        """Construit la valeur INDEX5 à partir d'une main"""
        if not all(key in main and main[key] is not None and main[key] != "" 
                  for key in ['index1', 'index2', 'index3']):
            return None
        return f"{main['index1']}_{main['index2']}_{main['index3']}"

    def construire_index6(self, main: Dict) -> str:
        """Construit la valeur INDEX6 à partir d'une main"""
        if not all(key in main and main[key] is not None and main[key] != "" 
                  for key in ['index1', 'index2']):
            return None
        index1_index2 = f"{main['index1']}_{main['index2']}"
        return self.index6_mapping.get(index1_index2)

    def construire_index7(self, main: Dict) -> str:
        """Construit la valeur INDEX7 à partir d'une main"""
        index6 = self.construire_index6(main)
        if index6 is None or not main.get('index3'):
            return None
        return f"{index6}_{main['index3']}"

    def construire_index1_index2(self, main: Dict) -> str:
        """Construit la valeur INDEX1_INDEX2 à partir d'une main"""
        if not all(key in main and main[key] is not None and main[key] != "" 
                  for key in ['index1', 'index2']):
            return None
        return f"{main['index1']}_{main['index2']}"
