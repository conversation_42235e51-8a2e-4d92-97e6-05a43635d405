#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INSTALLATEUR DE DÉPENDANCES - ANALYSEUR STATISTIQUE AVANCÉ
Installe automatiquement toutes les dépendances nécessaires
"""

import subprocess
import sys
import os

def installer_package(package_name, import_name=None):
    """Installe un package Python si nécessaire"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✅ {package_name} déjà installé")
        return True
    except ImportError:
        print(f"📦 Installation de {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ {package_name} installé avec succès")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ Erreur lors de l'installation de {package_name}")
            return False

def main():
    print("🚀 INSTALLATEUR DE DÉPENDANCES - ANALYSEUR STATISTIQUE AVANCÉ")
    print("=" * 80)
    
    # Liste des packages nécessaires
    packages = [
        ("numpy", "numpy"),
        ("pandas", "pandas"),
        ("scipy", "scipy"),
        ("psutil", "psutil"),
        ("matplotlib", "matplotlib"),
        ("seaborn", "seaborn")
    ]
    
    print("📦 Installation des dépendances Python...")
    
    success_count = 0
    for package_name, import_name in packages:
        if installer_package(package_name, import_name):
            success_count += 1
    
    print(f"\n📊 RÉSULTAT : {success_count}/{len(packages)} packages installés")
    
    if success_count == len(packages):
        print("✅ Toutes les dépendances sont installées !")
        print("\n🎯 PRÊT À UTILISER :")
        print("   python analyseur_statistique_avance.py dataset_baccarat_lupasco_20250704_170242_condensed.json")
    else:
        print("⚠️ Certaines dépendances n'ont pas pu être installées")
        print("💡 Essayez d'installer manuellement avec : pip install <package_name>")

if __name__ == "__main__":
    main()
