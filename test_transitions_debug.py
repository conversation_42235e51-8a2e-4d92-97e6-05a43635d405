#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST DEBUG - ANALYSEUR TRANSITIONS INDEX
"""

import json
from analyseur_transitions_index import AnalyseurTransitionsIndex, integrer_analyse_transitions

def test_direct():
    print("🔍 TEST DIRECT DE L'ANALYSEUR TRANSITIONS INDEX")
    print("=" * 80)
    
    # Charger les données
    print("📂 Chargement des données...")
    try:
        with open('dataset_baccarat_lupasco_20250704_180443_condensed.json', 'r', encoding='utf-8') as f:
            donnees = json.load(f)
        print(f"✅ Données chargées : {len(donnees.get('parties_condensees', []))} parties")
    except Exception as e:
        print(f"❌ Erreur de chargement : {e}")
        return
    
    # Test de l'analyseur
    analyseur = AnalyseurTransitionsIndex()
    
    if not analyseur.charger_donnees(donnees):
        print("❌ Échec du chargement des données")
        return
    
    # Test d'une partie
    if 'parties_condensees' in donnees and len(donnees['parties_condensees']) > 0:
        partie_test = donnees['parties_condensees'][0]
        print(f"\n🔍 EXAMEN DE LA PREMIÈRE PARTIE :")
        print(f"   Nombre de mains : {len(partie_test.get('mains', []))}")
        
        for i, main in enumerate(partie_test.get('mains', [])[:10]):
            print(f"   Main {i+1}: pos={main.get('main_number')}, idx1={main.get('index1')}, idx2={main.get('index2')}, idx3={main.get('index3')}")
    
    # Lancer l'analyse
    print(f"\n🚀 LANCEMENT DE L'ANALYSE...")
    resultats = analyseur.analyser_transitions_par_position()
    
    print(f"\n📊 RÉSULTATS :")
    print(f"   Total parties : {resultats.get('total_parties', 0)}")
    print(f"   Transitions INDEX5 : {len(resultats.get('transitions_index5', {}))}")
    print(f"   Transitions INDEX1_INDEX2 : {len(resultats.get('transitions_index1_index2', {}))}")

if __name__ == "__main__":
    test_direct()
