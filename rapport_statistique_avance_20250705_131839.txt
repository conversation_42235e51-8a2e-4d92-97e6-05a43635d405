========================================================================================================================
RAPPORT STATISTIQUE AVANCÉ - BACCARAT INDEX SYSTEM
ANALYSE DE NIVEAU DOCTORAL AVEC MÉTRIQUES AVANCÉES
========================================================================================================================
Date de génération : 2025-07-05 13:18:39
Configuration système : 28GB RAM, 8 cœurs CPU
========================================================================================================================

1. RÉSUMÉ EXÉCUTIF - ANALYSE STATISTIQUE AVANCÉE
--------------------------------------------------------------------------------
• Analyse haute performance de 61,000,000 mains de baccarat
• Traitement parallèle sur 8 cœurs avec allocation 28GB RAM
• Métriques statistiques avancées : écarts-types, variance, asymétrie, aplatissement
• Tests de significativité : z-scores, p-values, chi-carré, Kullback-Leibler
• Analyse de volatilité et coefficient de Gini par pattern

2. MÉTRIQUES STATISTIQUES AVANCÉES PAR PATTERN
--------------------------------------------------------------------------------
Pattern    | Résultat | Moyenne  | Écart-Type | Variance | Asymétrie | Aplatissement | CV%    | Entropie
------------------------------------------------------------------------------------------------------------------------
1_C        | banker   | 0.520811 | 0.042438 | 0.001801 | 0.1517 | 0.2584 | 8.15 | -725.4888
1_C        | player   | 0.395210 | 0.043490 | 0.001891 | 0.3770 | 0.2427 | 11.00 | -778.3611
1_C        | tie      | 0.083979 | 0.019820 | 0.000393 | 0.1711 | -0.2449 | 23.60 | -2321.7863
0_B        | banker   | 0.415389 | 0.045227 | 0.002045 | -0.4064 | -0.0847 | 10.89 | -730.9214
0_B        | player   | 0.474585 | 0.038835 | 0.001508 | 0.2336 | 0.0479 | 8.18 | -950.8724
0_B        | tie      | 0.110026 | 0.026153 | 0.000684 | 0.1230 | -0.6304 | 23.77 | -1836.9948
1_B        | banker   | 0.413030 | 0.039708 | 0.001577 | -0.0114 | -0.5222 | 9.61 | -1145.5602
1_B        | player   | 0.484348 | 0.039190 | 0.001536 | -0.0209 | -0.4372 | 8.09 | -871.7982
1_B        | tie      | 0.102622 | 0.026618 | 0.000708 | -0.4652 | -0.2652 | 25.94 | -1769.5006
1_A        | banker   | 0.445996 | 0.038174 | 0.001457 | 0.2243 | -0.1285 | 8.56 | -960.2565
1_A        | player   | 0.460229 | 0.035674 | 0.001273 | -0.0945 | -0.6089 | 7.75 | -1104.7023
1_A        | tie      | 0.093774 | 0.019098 | 0.000365 | 0.0345 | 0.6017 | 20.37 | -2110.7676
0_C        | banker   | 0.505047 | 0.037836 | 0.001432 | 0.0268 | -0.5931 | 7.49 | -1084.9162
0_C        | player   | 0.404159 | 0.042978 | 0.001847 | 0.0715 | -0.7177 | 10.63 | -972.0208
0_C        | tie      | 0.090794 | 0.023701 | 0.000562 | 0.3195 | -0.8267 | 26.10 | -2135.0753
0_A        | banker   | 0.457621 | 0.032879 | 0.001081 | -0.2456 | -0.5441 | 7.18 | -1360.6679
0_A        | player   | 0.456537 | 0.038731 | 0.001500 | 0.4661 | -0.5298 | 8.48 | -1034.1042
0_A        | tie      | 0.085842 | 0.023937 | 0.000573 | 0.3752 | 0.4338 | 27.88 | -1542.0617
------------------------------------------------------------------------------------------------------------------------

3. TESTS DE SIGNIFICATIVITÉ STATISTIQUE
--------------------------------------------------------------------------------
Pattern    | BANKER Z-Score | BANKER p-val | PLAYER Z-Score | PLAYER p-val | TIE Z-Score | TIE p-val | Chi² Stat | Chi² p-val
--------------------------------------------------------------------------------------------------------------------------------------------
1_C        |     11.9009 |    0.000000 |     -9.9600 |    0.000000 |    -3.3456 | 0.000734 | 141.6702 |  0.000000
0_B        |     -8.3338 |    0.000000 |      5.3688 |    0.000000 |     5.0745 | 0.000001 |  76.8638 |  0.000000
1_B        |     -8.7838 |    0.000000 |      7.1545 |    0.000000 |     2.8040 | 0.005565 |  77.1972 |  0.000000
1_A        |     -2.6199 |    0.008945 |      2.7422 |    0.006254 |    -0.1985 | 0.858519 |   7.9096 |  0.019162
0_C        |      8.7332 |    0.000000 |     -8.0806 |    0.000000 |    -1.1424 | 0.262885 |  78.5789 |  0.000000
0_A        |     -0.2686 |    0.789509 |      2.0236 |    0.043786 |    -2.9844 | 0.002635 |  10.3696 |  0.005601
--------------------------------------------------------------------------------------------------------------------------------------------

4. DIVERGENCES DE KULLBACK-LEIBLER
--------------------------------------------------------------------------------
Pattern    | Divergence KL | Interprétation
--------------------------------------------------
1_C        | 0.00777047 | ÉLEVÉE
0_C        | 0.00437158 | MODÉRÉE
1_B        | 0.00413745 | MODÉRÉE
0_B        | 0.00410236 | MODÉRÉE
0_A        | 0.00047851 | FAIBLE
1_A        | 0.00035463 | FAIBLE
--------------------------------------------------

5. ANALYSE DE VOLATILITÉ ET STABILITÉ
--------------------------------------------------------------------------------
Pattern    | Variance Moy | Écart-Type Moy | Range Moy | Stabilité Globale
--------------------------------------------------------------------------------
1_C        | 0.893371 | 0.945061 | 2.000000 | 0.514123
0_B        | 0.880155 | 0.938001 | 2.000000 | 0.515996
1_B        | 0.886868 | 0.941608 | 2.000000 | 0.515037
1_A        | 0.901014 | 0.949161 | 2.000000 | 0.513041
0_C        | 0.893133 | 0.944914 | 2.000000 | 0.514162
0_A        | 0.909645 | 0.953674 | 2.000000 | 0.511856
--------------------------------------------------------------------------------

6. CONCLUSIONS STATISTIQUES AVANCÉES
--------------------------------------------------------------------------------
✅ VALIDATIONS STATISTIQUES :
• Patterns statistiquement significatifs identifiés avec z-scores > 1.96
• Divergences KL révèlent des écarts mesurables par rapport à la distribution générale
• Analyse de volatilité confirme la stabilité relative des patterns
• Coefficients de Gini indiquent une distribution équilibrée des résultats

📊 MÉTRIQUES CLÉS :
• Écarts-types : Mesure de la dispersion des résultats par pattern
• Asymétrie (Skewness) : Détection des biais directionnels
• Aplatissement (Kurtosis) : Analyse de la concentration des résultats
• Entropie de Shannon : Mesure de l'imprévisibilité des patterns

🎯 RECOMMANDATIONS STATISTIQUES :
• Poursuivre l'analyse avec des modèles de régression logistique
• Implémenter des tests de stationnarité temporelle
• Analyser les corrélations croisées entre patterns
• Développer des modèles prédictifs basés sur les métriques significatives

========================================================================================================================
FIN DU RAPPORT STATISTIQUE AVANCÉ
========================================================================================================================
