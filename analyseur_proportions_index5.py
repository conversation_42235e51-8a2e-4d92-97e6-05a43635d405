#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR PROPORTIONS INDEX5, INDEX6 ET INDEX7 BACCARAT
======================================================

Analyse les proportions des valeurs d'INDEX5, INDEX6 et INDEX7 dans le dataset baccarat
pour vérifier la distribution et détecter d'éventuels biais.

Les 18 valeurs INDEX5 attendues :
- 0_A_BANKER, 1_A_BANKER, 0_B_BANKER, 1_B_BANKER, 0_C_BANKER, 1_C_BANKER
- 0_A_PLAYER, 1_A_PLAYER, 0_B_PLAYER, 1_B_PLAYER, 0_C_PLAYER, 1_C_PLAYER
- 0_A_TIE, 1_A_TIE, 0_B_TIE, 1_B_TIE, 0_C_TIE, 1_<PERSON>_TIE

Les 6 valeurs INDEX6 attendues :
- M (0_A), N (0_B), O (0_C), S (1_A), T (1_B), U (1_C)

Les 18 valeurs INDEX7 attendues (INDEX6_INDEX3) :
- M_BANKER, M_PLAYER, M_TIE, N_BANKER, N_PLAYER, N_TIE
- O_BANKER, O_PLAYER, O_TIE, S_BANKER, S_PLAYER, S_TIE
- T_BANKER, T_PLAYER, T_TIE, U_BANKER, U_PLAYER, U_TIE
"""

import json
import os
from collections import Counter, defaultdict
from datetime import datetime
from typing import Dict, List, Tuple

class AnalyseurProportionsIndex5:
    """Analyseur des proportions INDEX5, INDEX6 et INDEX7 dans le dataset baccarat"""
    
    def __init__(self):
        """Initialise l'analyseur"""
        # Définir les 18 valeurs INDEX5 attendues
        self.index5_attendues = [
            # BANKER
            "0_A_BANKER", "1_A_BANKER",
            "0_B_BANKER", "1_B_BANKER", 
            "0_C_BANKER", "1_C_BANKER",
            # PLAYER
            "0_A_PLAYER", "1_A_PLAYER",
            "0_B_PLAYER", "1_B_PLAYER",
            "0_C_PLAYER", "1_C_PLAYER", 
            # TIE
            "0_A_TIE", "1_A_TIE",
            "0_B_TIE", "1_B_TIE",
            "0_C_TIE", "1_C_TIE"
        ]
        
        # NOUVEAU : Les 6 valeurs INDEX6 attendues
        self.index6_attendues = ["M", "N", "O", "S", "T", "U"]

        # NOUVEAU : Les 18 valeurs INDEX7 attendues (INDEX6_INDEX3)
        self.index7_attendues = [
            # M (0_A) + BANKER/PLAYER/TIE
            "M_BANKER", "M_PLAYER", "M_TIE",
            # N (0_B) + BANKER/PLAYER/TIE
            "N_BANKER", "N_PLAYER", "N_TIE",
            # O (0_C) + BANKER/PLAYER/TIE
            "O_BANKER", "O_PLAYER", "O_TIE",
            # S (1_A) + BANKER/PLAYER/TIE
            "S_BANKER", "S_PLAYER", "S_TIE",
            # T (1_B) + BANKER/PLAYER/TIE
            "T_BANKER", "T_PLAYER", "T_TIE",
            # U (1_C) + BANKER/PLAYER/TIE
            "U_BANKER", "U_PLAYER", "U_TIE"
        ]

        # Compteurs
        self.compteur_index1 = Counter()  # NOUVEAU : Compteur INDEX1
        self.compteur_index2 = Counter()  # NOUVEAU : Compteur INDEX2
        self.compteur_index3 = Counter()  # NOUVEAU : Compteur INDEX3
        self.compteur_index5 = Counter()
        self.compteur_index6 = Counter()  # NOUVEAU : Compteur INDEX6
        self.compteur_index7 = Counter()  # NOUVEAU : Compteur INDEX7
        self.total_mains = 0
        self.mains_valides = 0
        self.mains_dummy = 0
        
        # Statistiques par catégorie
        self.stats_par_sync = defaultdict(int)      # 0 vs 1
        self.stats_par_cards = defaultdict(int)     # A vs B vs C
        self.stats_par_result = defaultdict(int)    # PLAYER vs BANKER vs TIE
        
        # NOUVEAU : Statistiques pour combinaisons INDEX1+INDEX2
        self.stats_par_sync_cards = defaultdict(int)  # 0_A, 0_B, 0_C, 1_A, 1_B, 1_C

    def charger_dataset(self, filename: str) -> bool:
        """
        Charge et analyse le dataset JSON
        
        Args:
            filename: Nom du fichier JSON à analyser
            
        Returns:
            bool: True si chargement réussi
        """
        if not os.path.exists(filename):
            print(f"❌ Fichier non trouvé : {filename}")
            return False
            
        print(f"📂 Chargement du dataset : {filename}")
        
        try:
            # Détecter la taille du fichier
            file_size = os.path.getsize(filename)
            file_size_gb = file_size / (1024 * 1024 * 1024)

            print(f"📏 Taille du fichier : {file_size_gb:.2f} GB")

            # NOUVEAU : Chargement haute performance avec cache 10GB
            print("🚀 Chargement haute performance avec cache 10GB RAM")
            return self._charger_donnees_cache_10gb(filename)

        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            return False

    def _charger_donnees_cache_10gb(self, filename: str) -> bool:
        """
        Chargement haute performance avec allocation de 10GB de RAM
        Optimisé pour les très gros fichiers JSON (10-12GB)
        """
        import gc
        import sys

        try:
            print("🧠 Allocation de 10GB de RAM pour le cache...")

            # Optimisations système pour performances maximales
            import os
            os.environ['PYTHONHASHSEED'] = '0'  # Hash déterministe

            # Forcer le garbage collection avant le chargement
            gc.collect()
            gc.disable()  # Désactiver GC pendant le chargement pour plus de vitesse

            # Lire tout le fichier en une fois avec un buffer optimisé
            print("📖 Lecture complète du fichier en mémoire...")

            # Obtenir la taille du fichier pour la barre de progression
            file_size = os.path.getsize(filename)

            with open(filename, 'r', encoding='utf-8', buffering=512*1024*1024) as f:  # Buffer 512MB optimisé
                print("⚡ Chargement du contenu complet...")
                print("📊 Progression du chargement :")

                # Lire par chunks pour afficher la progression
                content = ""
                chunk_size = 50 * 1024 * 1024  # 50MB par chunk pour progression
                bytes_read = 0

                while True:
                    chunk = f.read(chunk_size)
                    if not chunk:
                        break
                    content += chunk
                    bytes_read += len(chunk.encode('utf-8'))

                    # Afficher progression
                    progress = min(100, (bytes_read / file_size) * 100)
                    print(f"   📈 {progress:.1f}% chargé ({bytes_read:,} / {file_size:,} octets)", end='\r')

                print()  # Nouvelle ligne après progression

            print(f"✅ Fichier chargé en mémoire : {len(content):,} caractères")

            # Analyser le début du contenu pour détecter le format
            debut_content = content[:200]
            print(f"🔍 Début du fichier : {debut_content[:100]}...")

            # Détecter et corriger le format si nécessaire
            if content.startswith(',{"partie_number"'):
                print("🔧 Correction du format JSON mal formé (commence par virgule)...")
                # Le fichier se termine par ]} mais on va avoir ]}]} après correction
                if content.endswith(']}'):
                    content = content[:-2]  # Enlever ]}
                    print("🔧 Suppression du ]} final en trop")
                # Enlever la virgule du début et ajouter la structure JSON correcte
                content = '{"parties_condensees": [' + content[1:] + ']}'
                print("✅ Format JSON corrigé")
                
            elif content.startswith('{"partie_number"'):
                print("🔧 Correction du format JSON (array d'objets sans wrapper)...")
                # Ajouter le wrapper pour format condensé
                content = '{"parties_condensees": [' + content + ']}'
                print("✅ Format JSON corrigé")
                
            elif '"parties_condensees"' in debut_content:
                print("✅ Format JSON condensé détecté (correct)")
                
            elif '"parties"' in debut_content:
                print("✅ Format JSON standard détecté (correct)")
                
            else:
                # Tentative d'analyse plus approfondie
                print("🔍 Analyse approfondie du format...")
                
                # Chercher des patterns JSON valides dans le contenu
                if '{"partie_number":' in content[:1000]:
                    print("🔧 Pattern partie détecté, correction du format...")
                    # Trouver le premier objet partie complet
                    start_idx = content.find('{"partie_number":')
                    if start_idx != -1:
                        # Extraire tout à partir de la première partie
                        content_parties = content[start_idx:]
                        # Wrapper dans la structure attendue
                        content = '{"parties_condensees": [' + content_parties + ']}'
                        print("✅ Format JSON corrigé avec pattern détection")
                    else:
                        print("❌ Impossible de corriger le format")
                        return False
                else:
                    print("❌ Format JSON non reconnu - aucun pattern détecté")
                    print(f"🔍 Premier contenu: {content[:500]}")
                    return False

            # Parser le JSON avec optimisations
            print("🔄 Parsing JSON haute performance...")
            try:
                data = json.loads(content)
                print("✅ JSON parsé avec succès")
            except json.JSONDecodeError as e:
                print(f"❌ Erreur de parsing JSON : {e}")
                print(f"📍 Position : ligne {e.lineno}, colonne {e.colno}")
                # Afficher le contexte de l'erreur
                lines = content.split('\n')
                if e.lineno <= len(lines):
                    error_line = lines[e.lineno - 1] if e.lineno > 0 else "N/A"
                    print(f"🔍 Ligne d'erreur : {error_line[:100]}")
                return False

            # Libérer la mémoire du contenu brut
            del content
            gc.enable()  # Réactiver GC après chargement
            gc.collect()

            # Traiter les données
            if 'parties_condensees' in data:
                parties = data['parties_condensees']
                print("📊 Format détecté : Condensé (cache 10GB)")
                print(f"📊 Métadonnées : Format condensé sans métadonnées détaillées")
            elif 'parties' in data:
                parties = data['parties']
                print("📊 Format détecté : Standard (cache 10GB)")
            else:
                print("❌ Structure JSON invalide après correction")
                print(f"🔑 Clés disponibles : {list(data.keys())}")
                return False

            print(f"\n🎲 Analyse de {len(parties):,} parties...")

            # Traitement optimisé avec affichage de progression
            for i, partie in enumerate(parties):
                self._analyser_partie(partie)

                # Affichage du progrès tous les 100,000 parties
                if (i + 1) % 100000 == 0:
                    print(f"🔄 Parties traitées : {i + 1:,}/{len(parties):,}")

            print(f"✅ Dataset chargé avec succès en mode cache 10GB !")
            print(f"   • Total parties traitées : {len(parties):,}")
            print(f"   • Total mains analysées : {self.total_mains:,}")
            print(f"   • Mains valides : {self.mains_valides:,}")
            print(f"   • Mains dummy ignorées : {self.mains_dummy:,}")

            return True

        except MemoryError:
            print("❌ Erreur : Mémoire insuffisante pour le cache 10GB")
            print("💡 Suggestion : Fermer d'autres applications ou augmenter la RAM")
            return False
        except Exception as e:
            print(f"❌ Erreur lors du chargement cache 10GB : {e}")
            import traceback
            print(f"🔍 Traceback : {traceback.format_exc()}")
            return False

    def _charger_donnees_standard(self, filename: str) -> bool:
        """Chargement standard pour les fichiers de taille normale"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Vérifier la structure (compatible avec les deux formats)
            if 'parties' in data:
                # Format ancien
                parties = data['parties']
                print("📊 Format détecté : Standard (avec métadonnées)")

                # Analyser les métadonnées si disponibles
                metadata = data.get('metadata', {})
                print(f"📊 Métadonnées :")
                print(f"   • Générateur : {metadata.get('generateur', 'N/A')}")
                print(f"   • Version : {metadata.get('version', 'N/A')}")
                print(f"   • Date génération : {metadata.get('date_generation', 'N/A')}")
                print(f"   • Nombre parties : {metadata.get('nombre_parties', 'N/A')}")
                print(f"   • Hasard cryptographique : {metadata.get('hasard_cryptographique', 'N/A')}")

            elif 'parties_condensees' in data:
                # Format exemple.json
                parties = data['parties_condensees']
                print("📊 Format détecté : Condensé (exemple.json)")
                print(f"📊 Métadonnées : Format condensé sans métadonnées détaillées")

            else:
                print("❌ Structure JSON invalide : ni 'parties' ni 'parties_condensees' trouvées")
                return False
            print(f"\n🎲 Analyse de {len(parties)} parties...")

            for partie in parties:
                self._analyser_partie(partie)

            print(f"✅ Dataset chargé avec succès !")
            print(f"   • Total mains analysées : {self.total_mains}")
            print(f"   • Mains valides : {self.mains_valides}")
            print(f"   • Mains dummy ignorées : {self.mains_dummy}")

            return True

        except json.JSONDecodeError as e:
            print(f"❌ Erreur JSON : {e}")
            return False
        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            return False

    def _charger_donnees_streaming(self, filename: str) -> bool:
        """
        Chargement par streaming pour les très gros fichiers JSON
        Traite le fichier par chunks pour éviter les problèmes de mémoire
        """
        try:
            print("🔄 Initialisation du streaming JSON...")

            parties_count = 0
            chunk_size = 100000  # Traiter 100,000 parties à la fois pour plus de vitesse
            current_chunk = []

            with open(filename, 'r', encoding='utf-8') as f:
                # Lire plus de contenu au début pour détecter le format
                content = f.read(200)  # Lire 200 caractères au lieu de 50
                f.seek(0)

                print(f"🔍 Début du fichier : {content[:100]}...")

                if '"parties_condensees"' in content:
                    print("📊 Format détecté : Condensé (streaming)")
                    print("📊 Métadonnées : Format condensé sans métadonnées détaillées")

                    # Parser le JSON par streaming
                    parties_count = self._stream_parse_parties_condensees(f, chunk_size)

                elif '"parties"' in content:
                    print("📊 Format détecté : Standard (streaming)")
                    parties_count = self._stream_parse_parties_standard(f, chunk_size)

                elif content.startswith(',{"partie_number"'):
                    print("📊 Format détecté : Condensé mal formé (commence par virgule)")
                    print("📊 Correction automatique du format...")
                    print("📊 Métadonnées : Format condensé sans métadonnées détaillées")

                    # Parser le JSON mal formé par streaming
                    parties_count = self._stream_parse_parties_condensees_malformed(f, chunk_size)

                else:
                    print(f"❌ Format JSON non reconnu pour le streaming")
                    print(f"🔍 Contenu détecté : {content}")
                    return False

            print(f"\n✅ Dataset chargé avec succès en mode streaming !")
            print(f"   • Total parties traitées : {parties_count:,}")
            print(f"   • Total mains analysées : {self.total_mains:,}")
            print(f"   • Mains valides : {self.mains_valides:,}")
            print(f"   • Mains dummy ignorées : {self.mains_dummy:,}")

            return True

        except Exception as e:
            print(f"❌ Erreur lors du streaming : {e}")
            return False

    def _stream_parse_parties_condensees(self, file_handle, chunk_size: int) -> int:
        """
        Parse un fichier JSON avec parties_condensees par streaming
        Retourne le nombre de parties traitées
        """
        import re

        parties_count = 0
        buffer = ""
        partie_pattern = r'"partie_number":\s*(\d+)'

        print("🔄 Début du parsing par streaming...")

        # Lire le fichier par chunks plus gros pour plus de vitesse
        while True:
            chunk = file_handle.read(65536)  # Lire 64KB à la fois
            if not chunk:
                break

            buffer += chunk

            # Chercher les parties complètes dans le buffer
            while True:
                # Trouver le début d'une partie
                start_match = re.search(r'\{"partie_number":', buffer)
                if not start_match:
                    break

                start_pos = start_match.start()

                # Trouver la fin de cette partie (début de la suivante ou fin du tableau)
                next_start = re.search(r'\},\s*\{"partie_number":', buffer[start_pos + 1:])
                if next_start:
                    end_pos = start_pos + 1 + next_start.start() + 1  # +1 pour inclure la }
                else:
                    # Chercher la fin du tableau
                    end_match = re.search(r'\}\s*\]\s*\}', buffer[start_pos:])
                    if end_match:
                        end_pos = start_pos + end_match.start() + 1
                    else:
                        # Partie incomplète, garder dans le buffer
                        break

                # Extraire et parser cette partie
                partie_json = buffer[start_pos:end_pos]
                try:
                    partie = json.loads(partie_json)
                    self._analyser_partie(partie)
                    parties_count += 1

                    # Affichage du progrès tous les 100000 parties
                    if parties_count % 100000 == 0:
                        print(f"🔄 Parties traitées : {parties_count:,}")

                except json.JSONDecodeError:
                    # Ignorer les parties mal formées
                    pass

                # Retirer cette partie du buffer
                buffer = buffer[end_pos:]

        return parties_count

    def _stream_parse_parties_condensees_malformed(self, file_handle, chunk_size: int) -> int:
        """
        Parse un fichier JSON mal formé qui commence par une virgule
        (problème de génération par batch)
        """
        import re

        parties_count = 0
        buffer = ""

        print("🔄 Début du parsing par streaming (fichier mal formé)...")

        # Lire le fichier par chunks plus gros pour plus de vitesse
        while True:
            chunk = file_handle.read(65536)  # Lire 64KB à la fois
            if not chunk:
                break

            buffer += chunk

            # Chercher les parties complètes dans le buffer
            while True:
                # Trouver le début d'une partie (peut commencer par , ou {)
                start_match = re.search(r'[,\s]*\{"partie_number":', buffer)
                if not start_match:
                    break

                start_pos = start_match.start()
                # Ajuster pour commencer à la {
                brace_pos = buffer.find('{"partie_number":', start_pos)
                if brace_pos == -1:
                    break
                start_pos = brace_pos

                # Trouver la fin de cette partie
                brace_count = 0
                end_pos = start_pos
                in_string = False
                escape_next = False

                for i, char in enumerate(buffer[start_pos:], start_pos):
                    if escape_next:
                        escape_next = False
                        continue
                    if char == '\\':
                        escape_next = True
                        continue
                    if char == '"' and not escape_next:
                        in_string = not in_string
                        continue
                    if not in_string:
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                end_pos = i + 1
                                break

                if brace_count != 0:
                    # Partie incomplète, garder dans le buffer
                    break

                # Extraire et parser cette partie
                partie_json = buffer[start_pos:end_pos]
                try:
                    partie = json.loads(partie_json)
                    self._analyser_partie(partie)
                    parties_count += 1

                    # Affichage du progrès tous les 100000 parties
                    if parties_count % 100000 == 0:
                        print(f"🔄 Parties traitées : {parties_count:,}")

                except json.JSONDecodeError as e:
                    # Ignorer les parties mal formées
                    print(f"⚠️ Partie mal formée ignorée : {e}")

                # Retirer cette partie du buffer
                buffer = buffer[end_pos:]

        return parties_count

    def _stream_parse_parties_standard(self, file_handle, chunk_size: int) -> int:
        """
        Parse un fichier JSON avec parties standard par streaming
        Retourne le nombre de parties traitées
        """
        # Implémentation similaire pour le format standard
        # Pour l'instant, on utilise la méthode standard
        print("⚠️ Streaming pour format standard pas encore implémenté, utilisation standard")
        file_handle.seek(0)
        data = json.load(file_handle)
        parties = data.get('parties', [])

        for partie in parties:
            self._analyser_partie(partie)

        return len(parties)

    def _analyser_partie(self, partie: dict):
        """Analyse une partie spécifique"""
        # Détecter le format et récupérer les mains
        if 'mains' in partie:
            # Format standard
            mains = partie.get('mains', [])
        elif 'mains_condensees' in partie:
            # Format condensé
            mains = partie.get('mains_condensees', [])
        else:
            return

        for main in mains:
            self.total_mains += 1

            # Ignorer les mains dummy
            if self._est_main_dummy(main):
                self.mains_dummy += 1
                continue

            # Récupérer tous les INDEX
            index1 = main.get('index1', '')  # NOUVEAU : Récupérer INDEX1
            index2 = main.get('index2', '')  # NOUVEAU : Récupérer INDEX2
            index3 = main.get('index3', '')  # NOUVEAU : Récupérer INDEX3
            index5 = main.get('index5', '')
            index6 = main.get('index6', '')  # NOUVEAU : Récupérer INDEX6
            index7 = main.get('index7', '')  # NOUVEAU : Récupérer INDEX7

            if index5 == '':
                continue  # Ignorer les mains sans INDEX5

            self.mains_valides += 1

            # Compter tous les INDEX
            if index1 != '':  # NOUVEAU : Compter INDEX1 seulement s'il n'est pas vide
                self.compteur_index1[str(index1)] += 1
            if index2:  # NOUVEAU : Compter INDEX2 seulement s'il n'est pas vide
                self.compteur_index2[index2] += 1
            if index3:  # NOUVEAU : Compter INDEX3 seulement s'il n'est pas vide
                self.compteur_index3[index3] += 1
            self.compteur_index5[index5] += 1
            if index6:  # NOUVEAU : Compter INDEX6 seulement s'il n'est pas vide
                self.compteur_index6[index6] += 1
            if index7:  # NOUVEAU : Compter INDEX7 seulement s'il n'est pas vide
                self.compteur_index7[index7] += 1

            # Analyser les composants
            self._analyser_composants_index5(index5)

    def _est_main_dummy(self, main):
        """
        Détecte si une main est dummy avec compatibilité pour _append_batch_to_json()

        Cette méthode gère les deux cas :
        1. Format exemple.json : main dummy avec index vides ("")
        2. Format _append_batch_to_json() : main dummy avec vraies valeurs d'index
        """
        main_number = main.get('main_number')
        manche_pb_number = main.get('manche_pb_number')
        index2 = main.get('index2', '')
        index3 = main.get('index3', '')
        index5 = main.get('index5', '')

        # Cas 1: Format exemple.json - main_number = null
        if main_number is None:
            return True

        # Cas 2: Format _append_batch_to_json() - main_number = 0 (main dummy)
        if main_number == 0:
            return True

        # Cas 3: Détection par manche_pb_number = 0 (main dummy)
        if manche_pb_number == 0:
            return True

        # Cas 4: Détection par contenu des index (main dummy générée par Generateur60mains.py)
        if index3 == "DUMMY":
            return True

        if index2 == "dummy":
            return True

        if "_dummy_DUMMY" in index5:
            return True

        return False

    def _analyser_composants_index5(self, index5: str):
        """Analyse les composants d'un INDEX5"""
        try:
            parts = index5.split('_')
            if len(parts) == 3:
                sync, cards, result = parts
                
                # Compter par catégorie
                self.stats_par_sync[sync] += 1
                self.stats_par_cards[cards] += 1
                self.stats_par_result[result] += 1
                
                # NOUVEAU : Compter les combinaisons INDEX1+INDEX2
                sync_cards = f"{sync}_{cards}"
                self.stats_par_sync_cards[sync_cards] += 1
                
        except Exception:
            pass  # Ignorer les INDEX5 malformés
    
    def generer_rapport(self, filename_output: str = None):
        """
        Génère un rapport détaillé des proportions INDEX5, INDEX6 et INDEX7

        Args:
            filename_output: Nom du fichier de sortie (optionnel)
        """
        if filename_output is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename_output = f"rapport_proportions_index5_{timestamp}.txt"
        
        print(f"\n📊 Génération du rapport : {filename_output}")
        
        with open(filename_output, 'w', encoding='utf-8') as f:
            # En-tête
            f.write("RAPPORT PROPORTIONS INDEX5, INDEX6 ET INDEX7 BACCARAT\n")
            f.write("=" * 60 + "\n")
            f.write(f"Généré le : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total mains analysées : {self.total_mains:,}\n")
            f.write(f"Mains valides : {self.mains_valides:,}\n")
            f.write(f"Mains dummy ignorées : {self.mains_dummy:,}\n\n")
            
            # Vérifier les 18 valeurs attendues
            f.write("VÉRIFICATION DES 18 VALEURS INDEX5 ATTENDUES\n")
            f.write("=" * 50 + "\n")
            
            valeurs_trouvees = set(self.compteur_index5.keys())
            valeurs_attendues = set(self.index5_attendues)
            
            valeurs_manquantes = valeurs_attendues - valeurs_trouvees
            valeurs_inattendues = valeurs_trouvees - valeurs_attendues
            
            f.write(f"✅ Valeurs trouvées : {len(valeurs_trouvees)}/18\n")
            f.write(f"❌ Valeurs manquantes : {len(valeurs_manquantes)}\n")
            f.write(f"⚠️  Valeurs inattendues : {len(valeurs_inattendues)}\n\n")
            
            if valeurs_manquantes:
                f.write("VALEURS MANQUANTES :\n")
                for valeur in sorted(valeurs_manquantes):
                    f.write(f"   • {valeur}\n")
                f.write("\n")
            
            if valeurs_inattendues:
                f.write("VALEURS INATTENDUES :\n")
                for valeur in sorted(valeurs_inattendues):
                    count = self.compteur_index5[valeur]
                    pct = (count / self.mains_valides) * 100
                    f.write(f"   • {valeur} : {count:,} ({pct:.4f}%)\n")
                f.write("\n")
            
            # NOUVEAU : Vérifier les 6 valeurs INDEX6 attendues
            f.write("VÉRIFICATION DES 6 VALEURS INDEX6 ATTENDUES\n")
            f.write("=" * 50 + "\n")

            valeurs6_trouvees = set(self.compteur_index6.keys())
            valeurs6_attendues = set(self.index6_attendues)

            valeurs6_manquantes = valeurs6_attendues - valeurs6_trouvees
            valeurs6_inattendues = valeurs6_trouvees - valeurs6_attendues

            f.write(f"✅ Valeurs INDEX6 trouvées : {len(valeurs6_trouvees)}/6\n")
            f.write(f"❌ Valeurs INDEX6 manquantes : {len(valeurs6_manquantes)}\n")
            f.write(f"⚠️  Valeurs INDEX6 inattendues : {len(valeurs6_inattendues)}\n\n")

            if valeurs6_manquantes:
                f.write("VALEURS INDEX6 MANQUANTES :\n")
                for valeur in sorted(valeurs6_manquantes):
                    f.write(f"   • {valeur}\n")
                f.write("\n")

            if valeurs6_inattendues:
                f.write("VALEURS INDEX6 INATTENDUES :\n")
                for valeur in sorted(valeurs6_inattendues):
                    count = self.compteur_index6[valeur]
                    pct = (count / self.mains_valides) * 100
                    f.write(f"   • {valeur} : {count:,} ({pct:.4f}%)\n")
                f.write("\n")

            # NOUVEAU : Vérifier les 18 valeurs INDEX7 attendues
            f.write("VÉRIFICATION DES 18 VALEURS INDEX7 ATTENDUES\n")
            f.write("=" * 50 + "\n")

            valeurs7_trouvees = set(self.compteur_index7.keys())
            valeurs7_attendues = set(self.index7_attendues)

            valeurs7_manquantes = valeurs7_attendues - valeurs7_trouvees
            valeurs7_inattendues = valeurs7_trouvees - valeurs7_attendues

            f.write(f"✅ Valeurs INDEX7 trouvées : {len(valeurs7_trouvees)}/18\n")
            f.write(f"❌ Valeurs INDEX7 manquantes : {len(valeurs7_manquantes)}\n")
            f.write(f"⚠️  Valeurs INDEX7 inattendues : {len(valeurs7_inattendues)}\n\n")

            if valeurs7_manquantes:
                f.write("VALEURS INDEX7 MANQUANTES :\n")
                for valeur in sorted(valeurs7_manquantes):
                    f.write(f"   • {valeur}\n")
                f.write("\n")

            if valeurs7_inattendues:
                f.write("VALEURS INDEX7 INATTENDUES :\n")
                for valeur in sorted(valeurs7_inattendues):
                    count = self.compteur_index7[valeur]
                    pct = (count / self.mains_valides) * 100
                    f.write(f"   • {valeur} : {count:,} ({pct:.4f}%)\n")
                f.write("\n")
            
            # Proportions détaillées des 18 valeurs
            f.write("PROPORTIONS DÉTAILLÉES DES 18 VALEURS INDEX5\n")
            f.write("=" * 50 + "\n")
            f.write("Valeur INDEX5        | Count      | Pourcentage\n")
            f.write("-" * 45 + "\n")

            for index5 in self.index5_attendues:
                count = self.compteur_index5.get(index5, 0)
                pct_observe = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
                f.write(f"{index5:20s} | {count:10,d} | {pct_observe:9.4f}%\n")
            
            f.write("\n")
            
            # NOUVEAU : Proportions détaillées des 6 valeurs INDEX6
            f.write("PROPORTIONS DÉTAILLÉES DES 6 VALEURS INDEX6\n")
            f.write("=" * 50 + "\n")
            f.write("INDEX6 | Mapping     | Count      | Pourcentage\n")
            f.write("-" * 45 + "\n")

            # Mapping pour affichage
            index6_mapping_display = {
                'M': '0_A', 'N': '0_B', 'O': '0_C',
                'S': '1_A', 'T': '1_B', 'U': '1_C'
            }

            for index6 in self.index6_attendues:
                count = self.compteur_index6.get(index6, 0)
                pct_observe = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
                mapping = index6_mapping_display.get(index6, '???')
                f.write(f"{index6:6s} | {mapping:11s} | {count:10,d} | {pct_observe:9.4f}%\n")

            f.write("\n")

            # NOUVEAU : Proportions détaillées des 18 valeurs INDEX7
            f.write("PROPORTIONS DÉTAILLÉES DES 18 VALEURS INDEX7\n")
            f.write("=" * 50 + "\n")
            f.write("Valeur INDEX7        | INDEX6 | INDEX3 | Count      | Pourcentage\n")
            f.write("-" * 65 + "\n")

            for index7 in self.index7_attendues:
                count = self.compteur_index7.get(index7, 0)
                pct_observe = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0

                # Décomposer INDEX7 en INDEX6 et INDEX3
                if '_' in index7:
                    index6_part, index3_part = index7.split('_', 1)
                else:
                    index6_part, index3_part = index7, ''

                f.write(f"{index7:20s} | {index6_part:6s} | {index3_part:6s} | {count:10,d} | {pct_observe:9.4f}%\n")

            f.write("\n")
            
            # Analyse par composants
            self._ecrire_analyse_composants(f)
            
            # Classification en 3 groupes (original)
            self._ecrire_classification_groupes(f)

            # NOUVELLE CLASSIFICATION EN 6 CLASSES SYNC/RESULT
            self._ecrire_classification_6_classes(f)

            # NOUVEAU : ANALYSE INDEX6
            self._ecrire_analyse_index6(f)

            # NOUVEAU : ANALYSE INDEX7
            self._ecrire_analyse_index7(f)

            # NOUVEAU : ANALYSE DES ÉCARTS-TYPES POUR TOUS LES INDEX
            self._ecrire_analyse_ecarts_types(f)

            # CLASSIFICATION HIÉRARCHIQUE COMPLÈTE
            self._ecrire_classification_hierarchique(f)
        
        print(f"✅ Rapport généré : {filename_output}")
        return filename_output



    def _ecrire_analyse_composants(self, f):
        """Écrit l'analyse par composants"""
        f.write("ANALYSE PAR COMPOSANTS\n")
        f.write("=" * 30 + "\n")

        # INDEX1 (SYNC)
        f.write("INDEX1 (SYNC/DESYNC) :\n")
        total_sync = sum(self.stats_par_sync.values())
        for sync, count in sorted(self.stats_par_sync.items()):
            pct = (count / total_sync) * 100 if total_sync > 0 else 0
            f.write(f"   • {sync} : {count:,} ({pct:.4f}%)\n")
        f.write("\n")

        # INDEX2 (CARDS)
        f.write("INDEX2 (NOMBRE CARTES) :\n")
        total_cards = sum(self.stats_par_cards.values())
        for cards, count in sorted(self.stats_par_cards.items()):
            pct = (count / total_cards) * 100 if total_cards > 0 else 0
            cartes_nb = {"A": "4 cartes", "B": "6 cartes", "C": "5 cartes"}.get(cards, cards)
            f.write(f"   • {cards} ({cartes_nb}) : {count:,} ({pct:.4f}%)\n")
        f.write("\n")

        # INDEX3 (RESULT)
        f.write("INDEX3 (RÉSULTAT) :\n")
        total_result = sum(self.stats_par_result.values())
        for result, count in sorted(self.stats_par_result.items()):
            pct = (count / total_result) * 100 if total_result > 0 else 0
            f.write(f"   • {result} : {count:,} ({pct:.4f}%)\n")
        f.write("\n")

        # NOUVEAU : COMBINAISONS INDEX1+INDEX2 (SYNC_CARDS)
        f.write("COMBINAISONS INDEX1+INDEX2 (SYNC_CARDS) :\n")
        total_sync_cards = sum(self.stats_par_sync_cards.values())
        
        # Définir l'ordre d'affichage souhaité
        ordre_sync_cards = ["0_A", "0_B", "0_C", "1_A", "1_B", "1_C"]
        
        for sync_cards in ordre_sync_cards:
            count = self.stats_par_sync_cards.get(sync_cards, 0)
            pct = (count / total_sync_cards) * 100 if total_sync_cards > 0 else 0
            
            # Description détaillée
            sync_part, cards_part = sync_cards.split('_')
            sync_desc = "SYNC" if sync_part == "0" else "DESYNC"
            cards_desc = {"A": "4 cartes", "B": "6 cartes", "C": "5 cartes"}.get(cards_part, cards_part)
            
            f.write(f"   • {sync_cards} ({sync_desc} + {cards_desc}) : {count:,} ({pct:.4f}%)\n")
        f.write("\n")

        # ANALYSE COMPARATIVE SYNC vs DESYNC par nombre de cartes
        f.write("ANALYSE COMPARATIVE SYNC vs DESYNC PAR NOMBRE DE CARTES :\n")
        f.write("-" * 60 + "\n")
        
        for cards in ["A", "B", "C"]:
            sync_count = self.stats_par_sync_cards.get(f"0_{cards}", 0)
            desync_count = self.stats_par_sync_cards.get(f"1_{cards}", 0)
            total_cards_type = sync_count + desync_count
            
            if total_cards_type > 0:
                pct_sync = (sync_count / total_cards_type) * 100
                pct_desync = (desync_count / total_cards_type) * 100
                ecart = abs(pct_sync - pct_desync)
                
                cards_desc = {"A": "4 cartes (Naturels)", "B": "6 cartes (Double)", "C": "5 cartes (Simple)"}.get(cards, cards)
                
                f.write(f"• Catégorie {cards} ({cards_desc}) :\n")
                f.write(f"  - SYNC (0_{cards})   : {pct_sync:7.4f}% ({sync_count:,} mains)\n")
                f.write(f"  - DESYNC (1_{cards}) : {pct_desync:7.4f}% ({desync_count:,} mains)\n")
                f.write(f"  - TOTAL {cards}      : 100.0000% ({total_cards_type:,} mains)\n")
                f.write(f"  - ÉCART ÉQUILIBRE : {ecart:7.4f}%\n")
                f.write("\n")

        # RÉSUMÉ GLOBAL DES 6 COMBINAISONS
        f.write("RÉSUMÉ GLOBAL DES 6 COMBINAISONS INDEX1+INDEX2 :\n")
        f.write("-" * 50 + "\n")
        
        # Trier par count décroissant
        sync_cards_tries = sorted(self.stats_par_sync_cards.items(), key=lambda x: x[1], reverse=True)
        
        total_verification = 0
        for sync_cards, count in sync_cards_tries:
            pct = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
            total_verification += count
            
            # Description
            sync_part, cards_part = sync_cards.split('_')
            sync_desc = "SYNC" if sync_part == "0" else "DESYNC"
            cards_desc = {"A": "Naturels", "B": "Double", "C": "Simple"}.get(cards_part, cards_part)
            
            f.write(f"• {sync_cards} ({sync_desc:6s} + {cards_desc:8s}) : {pct:7.4f}% ({count:,} mains)\n")
        
        f.write(f"• TOTAL VÉRIFIÉ               : {(total_verification/self.mains_valides)*100:7.4f}% ({total_verification:,} mains)\n")
        f.write("\n")

    def _ecrire_classification_groupes(self, f):
        """Écrit la classification en 3 groupes"""
        f.write("CLASSIFICATION EN 3 GROUPES\n")
        f.write("=" * 30 + "\n")

        # Classifier les INDEX5 en 3 groupes
        groupe_tie = []
        groupe_a_pb = []
        groupe_bc_pb = []

        for index5, count in self.compteur_index5.items():
            if '_TIE' in index5:
                groupe_tie.append((index5, count))
            elif '_A_' in index5 and ('_PLAYER' in index5 or '_BANKER' in index5):
                groupe_a_pb.append((index5, count))
            elif ('_B_' in index5 or '_C_' in index5) and ('_PLAYER' in index5 or '_BANKER' in index5):
                groupe_bc_pb.append((index5, count))

        # Trier chaque groupe par count décroissant
        groupe_tie.sort(key=lambda x: x[1], reverse=True)
        groupe_a_pb.sort(key=lambda x: x[1], reverse=True)
        groupe_bc_pb.sort(key=lambda x: x[1], reverse=True)

        # Calculer les totaux
        total_tie = sum(count for _, count in groupe_tie)
        total_a_pb = sum(count for _, count in groupe_a_pb)
        total_bc_pb = sum(count for _, count in groupe_bc_pb)

        # Afficher les groupes
        pct_tie = (total_tie / self.mains_valides) * 100 if self.mains_valides > 0 else 0
        pct_a_pb = (total_a_pb / self.mains_valides) * 100 if self.mains_valides > 0 else 0
        pct_bc_pb = (total_bc_pb / self.mains_valides) * 100 if self.mains_valides > 0 else 0

        f.write(f"GROUPE 1 - TIE (RARE) : {total_tie:,} ({pct_tie:.4f}%)\n")
        for index5, count in groupe_tie:
            pct = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
            f.write(f"   • {index5} : {count:,} ({pct:.4f}%)\n")
        f.write("\n")

        f.write(f"GROUPE 2 - A + PLAYER/BANKER (MODÉRÉ) : {total_a_pb:,} ({pct_a_pb:.4f}%)\n")
        for index5, count in groupe_a_pb:
            pct = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
            f.write(f"   • {index5} : {count:,} ({pct:.4f}%)\n")
        f.write("\n")

        f.write(f"GROUPE 3 - B/C + PLAYER/BANKER (FRÉQUENT) : {total_bc_pb:,} ({pct_bc_pb:.4f}%)\n")
        for index5, count in groupe_bc_pb:
            pct = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
            f.write(f"   • {index5} : {count:,} ({pct:.4f}%)\n")
        f.write("\n")

        # Résumé des 3 classes
        f.write("RÉSUMÉ DES 3 CLASSES :\n")
        f.write("-" * 25 + "\n")
        f.write(f"• CLASSE RARE (TIE)     : {pct_tie:8.4f}% ({total_tie:,} mains)\n")
        f.write(f"• CLASSE MODÉRÉE (A+PB) : {pct_a_pb:8.4f}% ({total_a_pb:,} mains)\n")
        f.write(f"• CLASSE FRÉQUENTE (BC+PB): {pct_bc_pb:8.4f}% ({total_bc_pb:,} mains)\n")
        f.write(f"• TOTAL                 : {pct_tie + pct_a_pb + pct_bc_pb:8.4f}% ({self.mains_valides:,} mains)\n")
        f.write("\n")

    def _ecrire_classification_6_classes(self, f):
        """Écrit la classification en 6 classes SYNC/RESULT"""
        f.write("CLASSIFICATION EN 6 CLASSES SYNC/RESULT\n")
        f.write("=" * 40 + "\n")

        # Définir les 6 classes
        classes = {
            "SYNC_BANKER": [],
            "SYNC_PLAYER": [],
            "SYNC_TIE": [],
            "DESYNC_BANKER": [],
            "DESYNC_PLAYER": [],
            "DESYNC_TIE": []
        }

        # Classifier les INDEX5
        for index5, count in self.compteur_index5.items():
            if index5.startswith('0_') and '_BANKER' in index5:
                classes["SYNC_BANKER"].append((index5, count))
            elif index5.startswith('0_') and '_PLAYER' in index5:
                classes["SYNC_PLAYER"].append((index5, count))
            elif index5.startswith('0_') and '_TIE' in index5:
                classes["SYNC_TIE"].append((index5, count))
            elif index5.startswith('1_') and '_BANKER' in index5:
                classes["DESYNC_BANKER"].append((index5, count))
            elif index5.startswith('1_') and '_PLAYER' in index5:
                classes["DESYNC_PLAYER"].append((index5, count))
            elif index5.startswith('1_') and '_TIE' in index5:
                classes["DESYNC_TIE"].append((index5, count))

        # Afficher chaque classe
        totaux_classes = []

        for nom_classe, valeurs in classes.items():
            total_classe = sum(count for _, count in valeurs)
            pct_classe = (total_classe / self.mains_valides) * 100 if self.mains_valides > 0 else 0
            totaux_classes.append((nom_classe, total_classe, pct_classe))

            f.write(f"{nom_classe} : {total_classe:,} ({pct_classe:.4f}%)\n")

            # Trier par count décroissant
            valeurs_triees = sorted(valeurs, key=lambda x: x[1], reverse=True)

            for index5, count in valeurs_triees:
                pct = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
                f.write(f"   • {index5} : {count:,} ({pct:.4f}%)\n")
            f.write("\n")

        # Résumé des 6 classes
        f.write("RÉSUMÉ DES 6 CLASSES SYNC/RESULT :\n")
        f.write("-" * 40 + "\n")

        # Trier par pourcentage décroissant
        totaux_classes_tries = sorted(totaux_classes, key=lambda x: x[2], reverse=True)

        total_general = 0
        for nom_classe, total_classe, pct_classe in totaux_classes_tries:
            nom_court = nom_classe.replace("_", " ")
            f.write(f"• {nom_court:15s} : {pct_classe:8.4f}% ({total_classe:,} mains)\n")
            total_general += pct_classe

        f.write(f"• {'TOTAL':15s} : {total_general:8.4f}% ({self.mains_valides:,} mains)\n")

        # Analyse comparative SYNC vs DESYNC
        f.write("\nANALYSE SYNC vs DESYNC :\n")
        f.write("-" * 25 + "\n")

        # Calculer totaux SYNC vs DESYNC
        total_sync = sum(total for nom, total, _ in totaux_classes if nom.startswith("SYNC_"))
        total_desync = sum(total for nom, total, _ in totaux_classes if nom.startswith("DESYNC_"))

        pct_sync = (total_sync / self.mains_valides) * 100 if self.mains_valides > 0 else 0
        pct_desync = (total_desync / self.mains_valides) * 100 if self.mains_valides > 0 else 0

        f.write(f"• SYNC (0_*)   : {pct_sync:8.4f}% ({total_sync:,} mains)\n")
        f.write(f"• DESYNC (1_*) : {pct_desync:8.4f}% ({total_desync:,} mains)\n")
        f.write(f"• ÉQUILIBRE    : {abs(pct_sync - pct_desync):8.4f}% d'écart\n")
        f.write("\n")

    def _ecrire_analyse_index6(self, f):
        """Écrit l'analyse détaillée de l'INDEX6"""
        f.write("\n")
        f.write("ANALYSE DÉTAILLÉE INDEX6\n")
        f.write("=" * 30 + "\n")
        f.write("INDEX6 = Version condensée des combinaisons INDEX1+INDEX2\n")
        f.write("Mapping : 0_A=M, 0_B=N, 0_C=O, 1_A=S, 1_B=T, 1_C=U\n\n")
        
        # Correspondance INDEX6 ↔ INDEX1+INDEX2
        f.write("CORRESPONDANCE INDEX6 ↔ INDEX1+INDEX2 :\n")
        f.write("-" * 40 + "\n")
        
        correspondances = {
            'M': '0_A', 'N': '0_B', 'O': '0_C',
            'S': '1_A', 'T': '1_B', 'U': '1_C'
        }
        
        for index6, sync_cards in correspondances.items():
            count_index6 = self.compteur_index6.get(index6, 0)
            count_sync_cards = self.stats_par_sync_cards.get(sync_cards, 0)
            pct_index6 = (count_index6 / self.mains_valides) * 100 if self.mains_valides > 0 else 0
            
            # Vérification de cohérence
            coherence = "✅" if count_index6 == count_sync_cards else "❌"
            
            sync_part, cards_part = sync_cards.split('_')
            sync_desc = "SYNC" if sync_part == "0" else "DESYNC"
            cards_desc = {"A": "4 cartes", "B": "6 cartes", "C": "5 cartes"}.get(cards_part, cards_part)
            
            f.write(f"{coherence} {index6} ↔ {sync_cards} ({sync_desc} + {cards_desc})\n")
            f.write(f"   INDEX6 count : {count_index6:,}\n")
            f.write(f"   SYNC_CARDS count : {count_sync_cards:,}\n")
            f.write(f"   Pourcentage : {pct_index6:.4f}%\n")
            f.write("\n")
        
        # Analyse des groupes INDEX6
        f.write("ANALYSE PAR GROUPES INDEX6 :\n")
        f.write("-" * 30 + "\n")
        
        # Groupe SYNC (M, N, O)
        sync_values = ['M', 'N', 'O']
        total_sync = sum(self.compteur_index6.get(val, 0) for val in sync_values)
        pct_sync = (total_sync / self.mains_valides) * 100 if self.mains_valides > 0 else 0
        
        f.write(f"🔵 GROUPE SYNC (M, N, O) : {total_sync:,} ({pct_sync:.4f}%)\n")
        for val in sync_values:
            count = self.compteur_index6.get(val, 0)
            pct = (count / total_sync) * 100 if total_sync > 0 else 0
            pct_global = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
            f.write(f"   • {val} : {count:,} ({pct:.4f}% du groupe | {pct_global:.4f}% global)\n")
        f.write("\n")
        
        # Groupe DESYNC (S, T, U)
        desync_values = ['S', 'T', 'U']
        total_desync = sum(self.compteur_index6.get(val, 0) for val in desync_values)
        pct_desync = (total_desync / self.mains_valides) * 100 if self.mains_valides > 0 else 0
        
        f.write(f"🔴 GROUPE DESYNC (S, T, U) : {total_desync:,} ({pct_desync:.4f}%)\n")
        for val in desync_values:
            count = self.compteur_index6.get(val, 0)
            pct = (count / total_desync) * 100 if total_desync > 0 else 0
            pct_global = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
            f.write(f"   • {val} : {count:,} ({pct:.4f}% du groupe | {pct_global:.4f}% global)\n")
        f.write("\n")
        
        # Équilibre SYNC vs DESYNC via INDEX6
        f.write("ÉQUILIBRE SYNC vs DESYNC (via INDEX6) :\n")
        f.write("-" * 40 + "\n")
        ecart = abs(pct_sync - pct_desync)
        f.write(f"• SYNC (M,N,O)   : {pct_sync:8.4f}% ({total_sync:,} mains)\n")
        f.write(f"• DESYNC (S,T,U) : {pct_desync:8.4f}% ({total_desync:,} mains)\n")
        f.write(f"• ÉCART          : {ecart:8.4f}%\n")
        f.write(f"• TOTAL          : {pct_sync + pct_desync:8.4f}% ({total_sync + total_desync:,} mains)\n")
        f.write("\n")
        
        # Analyse par type de cartes via INDEX6
        f.write("ANALYSE PAR TYPE DE CARTES (via INDEX6) :\n")
        f.write("-" * 45 + "\n")
        
        types_cartes = {
            'A (4 cartes)': ['M', 'S'],  # 0_A=M, 1_A=S
            'B (6 cartes)': ['N', 'T'],  # 0_B=N, 1_B=T
            'C (5 cartes)': ['O', 'U']   # 0_C=O, 1_C=U
        }
        
        for type_nom, values in types_cartes.items():
            total_type = sum(self.compteur_index6.get(val, 0) for val in values)
            pct_type = (total_type / self.mains_valides) * 100 if self.mains_valides > 0 else 0
            
            sync_val, desync_val = values
            count_sync = self.compteur_index6.get(sync_val, 0)
            count_desync = self.compteur_index6.get(desync_val, 0)
            
            if total_type > 0:
                pct_sync_type = (count_sync / total_type) * 100
                pct_desync_type = (count_desync / total_type) * 100
                ecart_type = abs(pct_sync_type - pct_desync_type)
                
                f.write(f"• Type {type_nom} : {total_type:,} ({pct_type:.4f}%)\n")
                f.write(f"  - SYNC   ({sync_val}) : {pct_sync_type:7.4f}% ({count_sync:,})\n")
                f.write(f"  - DESYNC ({desync_val}) : {pct_desync_type:7.4f}% ({count_desync:,})\n")
                f.write(f"  - ÉCART        : {ecart_type:7.4f}%\n")
                f.write("\n")

    def _ecrire_analyse_index7(self, f):
        """Écrit l'analyse détaillée de l'INDEX7"""
        f.write("\n")
        f.write("ANALYSE DÉTAILLÉE INDEX7\n")
        f.write("=" * 30 + "\n")
        f.write("INDEX7 = INDEX6_INDEX3 (combinaison INDEX6 + résultat)\n")
        f.write("18 valeurs possibles : M/N/O/S/T/U × BANKER/PLAYER/TIE\n\n")

        # Analyse par INDEX6 (regroupement par INDEX6)
        f.write("ANALYSE PAR INDEX6 (regroupement par INDEX6) :\n")
        f.write("-" * 45 + "\n")

        # Regrouper par INDEX6
        index6_groups = {}
        for index7 in self.index7_attendues:
            if '_' in index7:
                index6_part, index3_part = index7.split('_', 1)
                if index6_part not in index6_groups:
                    index6_groups[index6_part] = []
                count = self.compteur_index7.get(index7, 0)
                index6_groups[index6_part].append((index7, index3_part, count))

        # Afficher chaque groupe INDEX6
        for index6 in ['M', 'N', 'O', 'S', 'T', 'U']:
            if index6 in index6_groups:
                group_data = index6_groups[index6]
                total_index6 = sum(count for _, _, count in group_data)
                pct_index6 = (total_index6 / self.mains_valides) * 100 if self.mains_valides > 0 else 0

                # Description du mapping
                mapping_desc = {
                    'M': '0_A (SYNC + 4 cartes)', 'N': '0_B (SYNC + 6 cartes)', 'O': '0_C (SYNC + 5 cartes)',
                    'S': '1_A (DESYNC + 4 cartes)', 'T': '1_B (DESYNC + 6 cartes)', 'U': '1_C (DESYNC + 5 cartes)'
                }.get(index6, index6)

                f.write(f"• INDEX6 {index6} ({mapping_desc}) : {total_index6:,} ({pct_index6:.4f}%)\n")

                # Trier par count décroissant
                group_data_sorted = sorted(group_data, key=lambda x: x[2], reverse=True)

                for index7, index3_part, count in group_data_sorted:
                    pct_global = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
                    pct_relatif = (count / total_index6) * 100 if total_index6 > 0 else 0
                    f.write(f"  - {index7:10s} : {count:,} ({pct_global:.4f}% | {pct_relatif:.4f}% du groupe {index6})\n")
                f.write("\n")

        # Analyse par INDEX3 (regroupement par résultat)
        f.write("ANALYSE PAR INDEX3 (regroupement par résultat) :\n")
        f.write("-" * 45 + "\n")

        # Regrouper par INDEX3
        index3_groups = {}
        for index7 in self.index7_attendues:
            if '_' in index7:
                index6_part, index3_part = index7.split('_', 1)
                if index3_part not in index3_groups:
                    index3_groups[index3_part] = []
                count = self.compteur_index7.get(index7, 0)
                index3_groups[index3_part].append((index7, index6_part, count))

        # Afficher chaque groupe INDEX3
        for index3 in ['BANKER', 'PLAYER', 'TIE']:
            if index3 in index3_groups:
                group_data = index3_groups[index3]
                total_index3 = sum(count for _, _, count in group_data)
                pct_index3 = (total_index3 / self.mains_valides) * 100 if self.mains_valides > 0 else 0

                f.write(f"• RÉSULTAT {index3} : {total_index3:,} ({pct_index3:.4f}%)\n")

                # Trier par count décroissant
                group_data_sorted = sorted(group_data, key=lambda x: x[2], reverse=True)

                for index7, index6_part, count in group_data_sorted:
                    pct_global = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
                    pct_relatif = (count / total_index3) * 100 if total_index3 > 0 else 0
                    f.write(f"  - {index7:10s} : {count:,} ({pct_global:.4f}% | {pct_relatif:.4f}% du {index3})\n")
                f.write("\n")

        # Analyse SYNC vs DESYNC via INDEX7
        f.write("ANALYSE SYNC vs DESYNC (via INDEX7) :\n")
        f.write("-" * 35 + "\n")

        sync_values = ['M', 'N', 'O']  # INDEX6 SYNC
        desync_values = ['S', 'T', 'U']  # INDEX6 DESYNC

        total_sync_index7 = 0
        total_desync_index7 = 0

        for index7 in self.compteur_index7:
            if '_' in index7:
                index6_part, _ = index7.split('_', 1)
                count = self.compteur_index7[index7]
                if index6_part in sync_values:
                    total_sync_index7 += count
                elif index6_part in desync_values:
                    total_desync_index7 += count

        pct_sync = (total_sync_index7 / self.mains_valides) * 100 if self.mains_valides > 0 else 0
        pct_desync = (total_desync_index7 / self.mains_valides) * 100 if self.mains_valides > 0 else 0
        ecart = abs(pct_sync - pct_desync)

        f.write(f"• SYNC (M,N,O_*)   : {pct_sync:8.4f}% ({total_sync_index7:,} mains)\n")
        f.write(f"• DESYNC (S,T,U_*) : {pct_desync:8.4f}% ({total_desync_index7:,} mains)\n")
        f.write(f"• ÉCART            : {ecart:8.4f}%\n")
        f.write(f"• TOTAL            : {pct_sync + pct_desync:8.4f}% ({total_sync_index7 + total_desync_index7:,} mains)\n")
        f.write("\n")

        # Cohérence INDEX7 vs INDEX5
        f.write("VÉRIFICATION COHÉRENCE INDEX7 vs INDEX5 :\n")
        f.write("-" * 40 + "\n")
        f.write("INDEX7 doit correspondre exactement à INDEX5 (même données, format différent)\n\n")

        # Vérifier la correspondance
        correspondances_correctes = 0
        correspondances_totales = 0

        for index5 in self.compteur_index5:
            if index5 and '_' in index5:
                parts = index5.split('_')
                if len(parts) == 3:
                    sync_part, cards_part, result_part = parts

                    # Convertir en INDEX7 attendu
                    index6_mapping = {
                        '0_A': 'M', '0_B': 'N', '0_C': 'O',
                        '1_A': 'S', '1_B': 'T', '1_C': 'U'
                    }
                    sync_cards = f"{sync_part}_{cards_part}"
                    index6_expected = index6_mapping.get(sync_cards, '')
                    index7_expected = f"{index6_expected}_{result_part}" if index6_expected else ''

                    count_index5 = self.compteur_index5[index5]
                    count_index7 = self.compteur_index7.get(index7_expected, 0)

                    correspondances_totales += 1
                    if count_index5 == count_index7:
                        correspondances_correctes += 1
                        status = "✅"
                    else:
                        status = "❌"

                    f.write(f"{status} {index5:12s} → {index7_expected:10s} | INDEX5: {count_index5:,} | INDEX7: {count_index7:,}\n")

        pct_coherence = (correspondances_correctes / correspondances_totales) * 100 if correspondances_totales > 0 else 0
        f.write(f"\nCOHÉRENCE GLOBALE : {correspondances_correctes}/{correspondances_totales} ({pct_coherence:.2f}%)\n")
        f.write("\n")

    def _ecrire_analyse_ecarts_types(self, f):
        """Écrit l'analyse des écarts-types pour tous les INDEX"""
        import math

        f.write("\n")
        f.write("ANALYSE DES ÉCARTS-TYPES POUR TOUS LES INDEX\n")
        f.write("=" * 50 + "\n")
        f.write("Mesure de la dispersion des proportions pour chaque INDEX\n")
        f.write("Plus l'écart-type est faible, plus la distribution est uniforme\n\n")

        # Définir les données pour chaque INDEX
        index_data = {
            "INDEX1": {
                "compteur": self.compteur_index1,
                "valeurs_attendues": [0, 1],
                "description": "SYNC/DESYNC (0=SYNC, 1=DESYNC)",
                "proportion_theorique": 50.0  # 50% chacun
            },
            "INDEX2": {
                "compteur": self.compteur_index2,
                "valeurs_attendues": ["A", "B", "C"],
                "description": "Catégories de cartes (A=4, B=6, C=5)",
                "proportion_theorique": 33.33  # ~33.33% chacun
            },
            "INDEX3": {
                "compteur": self.compteur_index3,
                "valeurs_attendues": ["BANKER", "PLAYER", "TIE"],
                "description": "Résultats du jeu",
                "proportion_theorique": None  # Pas uniforme (TIE ~9.5%, BANKER/PLAYER ~45% chacun)
            },
            "INDEX5": {
                "compteur": self.compteur_index5,
                "valeurs_attendues": self.index5_attendues,
                "description": "Combinaison INDEX1_INDEX2_INDEX3 (18 valeurs)",
                "proportion_theorique": 5.56  # ~5.56% chacun (100/18)
            },
            "INDEX6": {
                "compteur": self.compteur_index6,
                "valeurs_attendues": self.index6_attendues,
                "description": "Mapping INDEX1_INDEX2 (6 valeurs)",
                "proportion_theorique": 16.67  # ~16.67% chacun (100/6)
            },
            "INDEX7": {
                "compteur": self.compteur_index7,
                "valeurs_attendues": self.index7_attendues,
                "description": "Combinaison INDEX6_INDEX3 (18 valeurs)",
                "proportion_theorique": 5.56  # ~5.56% chacun (100/18)
            }
        }

        # Analyser chaque INDEX
        resultats_ecarts = []

        for nom_index, data in index_data.items():
            compteur = data["compteur"]
            valeurs_attendues = data["valeurs_attendues"]
            description = data["description"]
            proportion_theorique = data["proportion_theorique"]

            f.write(f"🔍 {nom_index} - {description}\n")
            f.write("-" * (len(nom_index) + len(description) + 5) + "\n")

            # Calculer les proportions observées
            proportions_observees = []
            total_observations = sum(compteur.values())

            if total_observations == 0:
                f.write("❌ Aucune donnée disponible pour cet INDEX\n\n")
                continue

            # Convertir les valeurs attendues en strings pour la comparaison
            valeurs_attendues_str = [str(v) for v in valeurs_attendues]

            f.write(f"Valeurs analysées : {len(valeurs_attendues_str)}\n")
            f.write(f"Total observations : {total_observations:,}\n")
            f.write(f"Détail des proportions :\n")

            for valeur in valeurs_attendues_str:
                count = compteur.get(valeur, 0)
                proportion = (count / total_observations) * 100 if total_observations > 0 else 0
                proportions_observees.append(proportion)

                # Afficher avec indication de déviation si proportion théorique disponible
                if proportion_theorique is not None:
                    deviation = proportion - proportion_theorique
                    signe = "+" if deviation >= 0 else ""
                    f.write(f"  • {valeur:12s} : {proportion:7.4f}% ({count:,} obs) [{signe}{deviation:+7.4f}% vs théorique]\n")
                else:
                    f.write(f"  • {valeur:12s} : {proportion:7.4f}% ({count:,} obs)\n")

            # Calculer l'écart-type
            if len(proportions_observees) > 1:
                moyenne = sum(proportions_observees) / len(proportions_observees)
                variance = sum((p - moyenne) ** 2 for p in proportions_observees) / len(proportions_observees)
                ecart_type = math.sqrt(variance)

                # Calculer l'écart-type théorique si applicable
                ecart_type_theorique = None
                if proportion_theorique is not None:
                    # Écart-type pour une distribution uniforme
                    ecart_type_theorique = 0.0  # Distribution parfaitement uniforme

                f.write(f"\n📊 STATISTIQUES :\n")
                f.write(f"  • Moyenne observée    : {moyenne:7.4f}%\n")
                f.write(f"  • Écart-type observé  : {ecart_type:7.4f}%\n")

                if proportion_theorique is not None:
                    f.write(f"  • Proportion théorique: {proportion_theorique:7.4f}%\n")
                    f.write(f"  • Écart-type théorique: {ecart_type_theorique:7.4f}% (distribution uniforme)\n")

                    # Coefficient de variation
                    cv = (ecart_type / moyenne) * 100 if moyenne > 0 else 0
                    f.write(f"  • Coefficient variation: {cv:7.4f}%\n")

                # Évaluation de l'uniformité
                if ecart_type < 1.0:
                    evaluation = "🟢 EXCELLENTE uniformité"
                elif ecart_type < 2.0:
                    evaluation = "🟡 BONNE uniformité"
                elif ecart_type < 5.0:
                    evaluation = "🟠 UNIFORMITÉ MODÉRÉE"
                else:
                    evaluation = "🔴 FAIBLE uniformité"

                f.write(f"  • Évaluation         : {evaluation}\n")

                # Stocker pour le résumé
                resultats_ecarts.append({
                    'nom': nom_index,
                    'ecart_type': ecart_type,
                    'moyenne': moyenne,
                    'nb_valeurs': len(proportions_observees),
                    'evaluation': evaluation,
                    'proportion_theorique': proportion_theorique
                })
            else:
                f.write(f"\n❌ Impossible de calculer l'écart-type (moins de 2 valeurs)\n")

            f.write("\n")

        # Résumé comparatif des écarts-types
        f.write("RÉSUMÉ COMPARATIF DES ÉCARTS-TYPES\n")
        f.write("=" * 40 + "\n")

        if resultats_ecarts:
            # Trier par écart-type croissant (meilleure uniformité en premier)
            resultats_tries = sorted(resultats_ecarts, key=lambda x: x['ecart_type'])

            f.write("Classement par uniformité (écart-type croissant) :\n\n")

            for i, resultat in enumerate(resultats_tries, 1):
                nom = resultat['nom']
                ecart_type = resultat['ecart_type']
                moyenne = resultat['moyenne']
                nb_valeurs = resultat['nb_valeurs']
                evaluation = resultat['evaluation']

                f.write(f"{i}. {nom:8s} : σ={ecart_type:6.4f}% | μ={moyenne:6.4f}% | n={nb_valeurs:2d} | {evaluation}\n")

            # Statistiques globales
            ecarts_types = [r['ecart_type'] for r in resultats_ecarts]
            ecart_moyen = sum(ecarts_types) / len(ecarts_types)
            ecart_min = min(ecarts_types)
            ecart_max = max(ecarts_types)

            f.write(f"\n📈 STATISTIQUES GLOBALES :\n")
            f.write(f"  • Écart-type minimum  : {ecart_min:7.4f}% (meilleure uniformité)\n")
            f.write(f"  • Écart-type maximum  : {ecart_max:7.4f}% (moins bonne uniformité)\n")
            f.write(f"  • Écart-type moyen    : {ecart_moyen:7.4f}%\n")
            f.write(f"  • Amplitude           : {ecart_max - ecart_min:7.4f}%\n")

            # Recommandations
            f.write(f"\n💡 INTERPRÉTATION :\n")
            if ecart_moyen < 2.0:
                f.write("  • Distribution globalement uniforme dans tous les INDEX\n")
            elif ecart_moyen < 5.0:
                f.write("  • Distribution acceptable avec quelques déséquilibres\n")
            else:
                f.write("  • Distribution présentant des déséquilibres significatifs\n")

            # INDEX les plus/moins uniformes
            plus_uniforme = resultats_tries[0]
            moins_uniforme = resultats_tries[-1]

            f.write(f"  • INDEX le plus uniforme  : {plus_uniforme['nom']} (σ={plus_uniforme['ecart_type']:.4f}%)\n")
            f.write(f"  • INDEX le moins uniforme : {moins_uniforme['nom']} (σ={moins_uniforme['ecart_type']:.4f}%)\n")
        else:
            f.write("❌ Aucune donnée disponible pour l'analyse des écarts-types\n")

        f.write("\n")

    def _ecrire_classification_hierarchique(self, f):
        """Écrit la classification hiérarchique complète"""
        f.write("CLASSIFICATION HIÉRARCHIQUE COMPLÈTE\n")
        f.write("=" * 40 + "\n")
        f.write("Structure : SYNC/DESYNC → RESULT → CARDS\n\n")

        # Définir la hiérarchie
        hierarchie = {
            "sync": {
                "nom": "SYNC (INDEX1=0)",
                "sous_classes": {
                    "banker": {
                        "nom": "BANKER",
                        "valeurs": ["0_A_BANKER", "0_B_BANKER", "0_C_BANKER"]
                    },
                    "player": {
                        "nom": "PLAYER",
                        "valeurs": ["0_A_PLAYER", "0_B_PLAYER", "0_C_PLAYER"]
                    },
                    "tie": {
                        "nom": "TIE",
                        "valeurs": ["0_A_TIE", "0_B_TIE", "0_C_TIE"]
                    }
                }
            },
            "desync": {
                "nom": "DESYNC (INDEX1=1)",
                "sous_classes": {
                    "banker": {
                        "nom": "BANKER",
                        "valeurs": ["1_A_BANKER", "1_B_BANKER", "1_C_BANKER"]
                    },
                    "player": {
                        "nom": "PLAYER",
                        "valeurs": ["1_A_PLAYER", "1_B_PLAYER", "1_C_PLAYER"]
                    },
                    "tie": {
                        "nom": "TIE",
                        "valeurs": ["1_A_TIE", "1_B_TIE", "1_C_TIE"]
                    }
                }
            }
        }

        # Analyser chaque niveau hiérarchique
        for sync_key, sync_data in hierarchie.items():
            # NIVEAU 1 : Classe principale
            total_niveau1 = sum(self.compteur_index5.get(index5, 0)
                              for sous_classe in sync_data["sous_classes"].values()
                              for index5 in sous_classe["valeurs"])

            pct_niveau1 = (total_niveau1 / self.mains_valides) * 100 if self.mains_valides > 0 else 0

            f.write(f"🔴 {sync_data['nom']} : {total_niveau1:,} ({pct_niveau1:.4f}%)\n")

            # NIVEAU 2 : Sous-classes
            for result_key, result_data in sync_data["sous_classes"].items():
                total_niveau2 = sum(self.compteur_index5.get(index5, 0) for index5 in result_data["valeurs"])
                pct_niveau2 = (total_niveau2 / self.mains_valides) * 100 if self.mains_valides > 0 else 0
                pct_relatif_niveau2 = (total_niveau2 / total_niveau1) * 100 if total_niveau1 > 0 else 0

                f.write(f"├── {result_data['nom']} : {total_niveau2:,} ({pct_niveau2:.4f}% | {pct_relatif_niveau2:.4f}% du parent)\n")

                # NIVEAU 3 : Valeurs finales (triées par count décroissant)
                valeurs_avec_counts = [(index5, self.compteur_index5.get(index5, 0)) for index5 in result_data["valeurs"]]
                valeurs_triees = sorted(valeurs_avec_counts, key=lambda x: x[1], reverse=True)

                for i, (index5, count) in enumerate(valeurs_triees):
                    pct_niveau3 = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
                    pct_relatif_niveau3 = (count / total_niveau2) * 100 if total_niveau2 > 0 else 0

                    # Symbole pour le dernier élément
                    symbole = "└──" if i == len(valeurs_triees) - 1 else "├──"

                    f.write(f"│   {symbole} {index5} : {count:,} ({pct_niveau3:.4f}% | {pct_relatif_niveau3:.4f}% du parent)\n")

                f.write("│\n")

            f.write("\n")

        # Résumé hiérarchique
        self._ecrire_resume_hierarchique(f, hierarchie)

    def _ecrire_resume_hierarchique(self, f, hierarchie):
        """Écrit le résumé de l'analyse hiérarchique"""
        f.write("RÉSUMÉ HIÉRARCHIQUE :\n")
        f.write("-" * 25 + "\n")

        total_hierarchique = 0
        for sync_key, sync_data in hierarchie.items():
            total_niveau1 = sum(self.compteur_index5.get(index5, 0)
                              for sous_classe in sync_data["sous_classes"].values()
                              for index5 in sous_classe["valeurs"])
            pct_niveau1 = (total_niveau1 / self.mains_valides) * 100 if self.mains_valides > 0 else 0
            total_hierarchique += pct_niveau1

            nom_court = "SYNC" if sync_key == "sync" else "DESYNC"
            f.write(f"• {nom_court:6s} : {pct_niveau1:8.4f}% ({total_niveau1:,} mains)\n")

        f.write(f"• TOTAL : {total_hierarchique:8.4f}% ({self.mains_valides:,} mains)\n")
        f.write("\n")

        # Analyse par résultat
        f.write("ANALYSE PAR RÉSULTAT :\n")
        f.write("-" * 25 + "\n")

        # Calculer totaux par résultat
        resultats_totaux = {
            "BANKER": {"sync": 0, "desync": 0},
            "PLAYER": {"sync": 0, "desync": 0},
            "TIE": {"sync": 0, "desync": 0}
        }

        for index5, count in self.compteur_index5.items():
            if '_BANKER' in index5:
                if index5.startswith('0_'):
                    resultats_totaux["BANKER"]["sync"] += count
                elif index5.startswith('1_'):
                    resultats_totaux["BANKER"]["desync"] += count
            elif '_PLAYER' in index5:
                if index5.startswith('0_'):
                    resultats_totaux["PLAYER"]["sync"] += count
                elif index5.startswith('1_'):
                    resultats_totaux["PLAYER"]["desync"] += count
            elif '_TIE' in index5:
                if index5.startswith('0_'):
                    resultats_totaux["TIE"]["sync"] += count
                elif index5.startswith('1_'):
                    resultats_totaux["TIE"]["desync"] += count

        for result_key in ["BANKER", "PLAYER", "TIE"]:
            total_result = resultats_totaux[result_key]["sync"] + resultats_totaux[result_key]["desync"]
            pct_total = (total_result / self.mains_valides) * 100 if self.mains_valides > 0 else 0

            f.write(f"• {result_key} : {pct_total:8.4f}% ({total_result:,} mains)\n")

            for sync_type in ["sync", "desync"]:
                count = resultats_totaux[result_key][sync_type]
                pct = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
                pct_relatif = (count / total_result) * 100 if total_result > 0 else 0
                sync_nom = "SYNC" if sync_type == "sync" else "DESYNC"

                f.write(f"  - {sync_nom:6s} : {pct:8.4f}% ({pct_relatif:7.4f}% du {result_key})\n")
            f.write("\n")

    def _ecrire_analyse_sous_sous_classes(self, f, hierarchie):
        """Écrit l'analyse détaillée des sous-sous-classes A, B, C"""
        f.write("ANALYSE DES SOUS-SOUS-CLASSES (A, B, C)\n")
        f.write("=" * 40 + "\n")
        f.write("A = 4 cartes, B = 6 cartes, C = 5 cartes\n\n")

        # Analyser les catégories A, B, C
        categories_data = {
            'A': {'total': 0, 'details': [], 'sync': 0, 'desync': 0, 'banker': 0, 'player': 0, 'tie': 0},
            'B': {'total': 0, 'details': [], 'sync': 0, 'desync': 0, 'banker': 0, 'player': 0, 'tie': 0},
            'C': {'total': 0, 'details': [], 'sync': 0, 'desync': 0, 'banker': 0, 'player': 0, 'tie': 0}
        }

        # Classifier les INDEX5 par catégorie
        for index5, count in self.compteur_index5.items():
            if '_A_' in index5:
                cat = 'A'
            elif '_B_' in index5:
                cat = 'B'
            elif '_C_' in index5:
                cat = 'C'
            else:
                continue

            categories_data[cat]['total'] += count
            categories_data[cat]['details'].append({'index5': index5, 'count': count})

            # Analyser SYNC/DESYNC
            if index5.startswith('0_'):
                categories_data[cat]['sync'] += count
            elif index5.startswith('1_'):
                categories_data[cat]['desync'] += count

            # Analyser résultats
            if '_BANKER' in index5:
                categories_data[cat]['banker'] += count
            elif '_PLAYER' in index5:
                categories_data[cat]['player'] += count
            elif '_TIE' in index5:
                categories_data[cat]['tie'] += count

        # Trier par total décroissant
        categories_triees = sorted(categories_data.items(), key=lambda x: x[1]['total'], reverse=True)

        for categorie, data in categories_triees:
            total_cat = data['total']
            pct_cat = (total_cat / self.mains_valides) * 100 if self.mains_valides > 0 else 0

            description = {"A": "4 cartes", "B": "6 cartes", "C": "5 cartes"}[categorie]

            f.write(f"🔸 CATÉGORIE {categorie} - {description} : {total_cat:,} ({pct_cat:.4f}%)\n")

            # Afficher répartition SYNC/DESYNC
            sync_totaux = {"SYNC": data['sync'], "DESYNC": data['desync']}
            for sync_type, count in sync_totaux.items():
                pct = (count / total_cat) * 100 if total_cat > 0 else 0
                f.write(f"  • {sync_type:6s} : {count:,} ({pct:.4f}% de la catégorie {categorie})\n")

            # Afficher répartition par résultat
            f.write(f"  • Répartition par résultat :\n")
            result_totaux = {"BANKER": data['banker'], "PLAYER": data['player'], "TIE": data['tie']}
            for result_type, count in sorted(result_totaux.items(), key=lambda x: x[1], reverse=True):
                pct = (count / total_cat) * 100 if total_cat > 0 else 0
                f.write(f"    - {result_type:7s} : {count:,} ({pct:.4f}%)\n")

            # Détail des valeurs pour cette catégorie (triées par count)
            details_tries = sorted(data['details'], key=lambda x: x['count'], reverse=True)
            f.write(f"  • Détail des valeurs :\n")
            for detail in details_tries:
                pct_detail = (detail['count'] / self.mains_valides) * 100 if self.mains_valides > 0 else 0
                pct_relatif = (detail['count'] / total_cat) * 100 if total_cat > 0 else 0
                f.write(f"    - {detail['index5']:12s} : {detail['count']:,} ({pct_detail:.4f}% | {pct_relatif:.4f}% de {categorie})\n")

            f.write("\n")

        # Résumé comparatif des 3 catégories
        f.write("RÉSUMÉ COMPARATIF DES CATÉGORIES :\n")
        f.write("-" * 35 + "\n")

        total_general = sum(data['total'] for _, data in categories_data.items())

        for categorie, data in categories_triees:
            total_cat = data['total']
            pct_global = (total_cat / self.mains_valides) * 100 if self.mains_valides > 0 else 0

            description = {"A": "4 cartes", "B": "6 cartes", "C": "5 cartes"}[categorie]

            f.write(f"• {categorie} ({description:12s}) : {pct_global:8.4f}% ({total_cat:,} mains)\n")

        f.write(f"• TOTAL VÉRIFIÉ        : {(total_general/self.mains_valides)*100:8.4f}% ({total_general:,} mains)\n")
        f.write("\n")

        for categorie, data in categories_triees:
            sync_count = sum(d['count'] for d in data['details'] if d['sync'] == 'SYNC')
            desync_count = sum(d['count'] for d in data['details'] if d['sync'] == 'DESYNC')
            total_cat = sync_count + desync_count

            if total_cat > 0:
                pct_sync = (sync_count / total_cat) * 100
                pct_desync = (desync_count / total_cat) * 100
                ecart = abs(pct_sync - pct_desync)

                f.write(f"• Catégorie {categorie} :\n")
                f.write(f"  - SYNC   : {pct_sync:7.4f}% ({sync_count:,})\n")
                f.write(f"  - DESYNC : {pct_desync:7.4f}% ({desync_count:,})\n")
                f.write(f"  - ÉCART  : {ecart:7.4f}%\n")
                f.write("\n")

def main():
    """Fonction principale"""
    print("🎯 ANALYSEUR PROPORTIONS INDEX5, INDEX6 ET INDEX7 BACCARAT")
    print("=" * 60)

    # Nom du fichier dataset (format exemple.json)
    filename = "dataset_baccarat_lupasco_20250704_170242_condensed.json"

    # Créer l'analyseur
    analyseur = AnalyseurProportionsIndex5()

    # Charger et analyser le dataset
    if analyseur.charger_dataset(filename):
        # Générer le rapport
        rapport_file = analyseur.generer_rapport()

        print(f"\n🎉 ANALYSE TERMINÉE !")
        print(f"📊 Rapport détaillé : {rapport_file}")
        print(f"🔍 Consultez le fichier pour voir les proportions exactes des 18 valeurs INDEX5")
    else:
        print("❌ Échec de l'analyse")


if __name__ == "__main__":
    main()
