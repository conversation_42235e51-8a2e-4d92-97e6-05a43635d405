#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SCRIPT DE TEST - ANALYSEURS BACCARAT
Teste les analyseurs avec un petit échantillon de données
"""

import json
import os
import sys
from datetime import datetime

def creer_dataset_test():
    """Crée un petit dataset de test"""
    print("🔧 Création du dataset de test...")
    
    # Données de test simulées
    dataset_test = {
        "parties_condensees": []
    }
    
    # Créer 10 parties avec 60 mains chacune
    for partie_num in range(1, 11):
        partie = {
            "partie_number": partie_num,
            "index1_brulage": partie_num % 2,  # Alternance 0/1
            "mains_condensees": []
        }
        
        # Créer 60 mains par partie
        for main_num in range(1, 61):
            # Simulation des index selon les règles
            index1 = (partie_num + main_num) % 2
            index2 = ['A', 'B', 'C'][main_num % 3]
            index3 = ['BANKER', 'PLAYER', 'TIE'][main_num % 3]
            
            main = {
                "main_number": main_num,
                "index1": index1,
                "index2": index2,
                "index3": index3
            }
            partie["mains_condensees"].append(main)
        
        dataset_test["parties_condensees"].append(partie)
    
    # Sauvegarder le dataset de test
    filename_test = "dataset_test_baccarat.json"
    with open(filename_test, 'w', encoding='utf-8') as f:
        json.dump(dataset_test, f, ensure_ascii=False, indent=2)
    
    print(f"✅ Dataset de test créé : {filename_test}")
    print(f"   • 10 parties")
    print(f"   • 600 mains total")
    print(f"   • Taille : {os.path.getsize(filename_test) / 1024:.1f} KB")
    
    return filename_test

def tester_analyseur_biais(filename_test):
    """Teste l'analyseur de biais systématique"""
    print(f"\n🧪 TEST DE L'ANALYSEUR DE BIAIS SYSTÉMATIQUE")
    print("-" * 60)
    
    try:
        # Import de l'analyseur
        from analyseur_biais_systematique import AnalyseurBiaisSystematique
        
        # Création et test
        analyseur = AnalyseurBiaisSystematique()
        
        print("📖 Chargement du dataset de test...")
        if not analyseur.charger_donnees(filename_test):
            print("❌ Échec du chargement")
            return False
        
        print("🔍 Test des analyses principales...")
        
        # Test analyse transitions
        result_transitions = analyseur.analyser_transitions_index1()
        if len(result_transitions) == 5:
            transitions_count, transitions_par_index2, etats_initiaux, total_mains, total_transitions = result_transitions
            print(f"   ✅ Analyse transitions : {len(transitions_count)} types de transitions, {total_mains:,} mains, {total_transitions:,} transitions")
        else:
            # Compatibilité avec l'ancien format
            transitions_count, transitions_par_index2, etats_initiaux = result_transitions
            print(f"   ✅ Analyse transitions : {len(transitions_count)} types de transitions (format ancien)")
        
        # Test corrélations
        correlations = analyseur.analyser_correlations_index2_index3()
        print(f"   ✅ Analyse corrélations : {len(correlations)} catégories INDEX2")
        
        # Test séquences
        sequences, runs_sync, runs_desync = analyseur.analyser_sequences_index1()
        print(f"   ✅ Analyse séquences : {len(sequences)} états analysés")
        
        print("✅ Analyseur de biais systématique : FONCTIONNEL")
        return True
        
    except Exception as e:
        print(f"❌ Erreur dans l'analyseur de biais : {e}")
        return False

def tester_analyseur_statistique(filename_test):
    """Teste l'analyseur statistique avancé"""
    print(f"\n🧪 TEST DE L'ANALYSEUR STATISTIQUE AVANCÉ")
    print("-" * 60)
    
    try:
        # Vérifier les dépendances
        print("📦 Vérification des dépendances...")
        import numpy as np
        import pandas as pd
        from scipy import stats
        import psutil
        print("   ✅ Toutes les dépendances disponibles")
        
        # Import de l'analyseur
        from analyseur_statistique_avance import AnalyseurStatistiqueAvance
        
        # Création et test (configuration réduite pour le test)
        analyseur = AnalyseurStatistiqueAvance(max_ram_gb=2, nb_coeurs=2)
        
        print("📖 Chargement du dataset de test...")
        if not analyseur.charger_donnees_haute_performance(filename_test):
            print("❌ Échec du chargement")
            return False
        
        print("🔍 Test des analyses statistiques...")
        
        # Test statistiques de base
        data_test = [0.1, 0.2, 0.15, 0.18, 0.12, 0.22, 0.16, 0.19]
        stats_test = analyseur.calculer_statistiques_avancees(data_test)
        print(f"   ✅ Statistiques avancées : {len(stats_test)} métriques calculées")
        
        # Test matrices de corrélation
        correlations = analyseur.calculer_matrices_correlation_avancees()
        print(f"   ✅ Matrices de corrélation : {len(correlations)} types calculés")
        
        print("✅ Analyseur statistique avancé : FONCTIONNEL")
        return True
        
    except ImportError as e:
        print(f"❌ Dépendance manquante : {e}")
        print("💡 Exécutez : python installer_dependances.py")
        return False
    except Exception as e:
        print(f"❌ Erreur dans l'analyseur statistique : {e}")
        return False

def nettoyer_fichiers_test():
    """Nettoie les fichiers de test"""
    fichiers_a_supprimer = [
        "dataset_test_baccarat.json",
        "rapport_analyse_baccarat_test.txt",
        "rapport_statistique_avance_test.txt"
    ]
    
    print(f"\n🧹 NETTOYAGE DES FICHIERS DE TEST")
    print("-" * 50)
    
    for fichier in fichiers_a_supprimer:
        if os.path.exists(fichier):
            os.remove(fichier)
            print(f"🗑️ Supprimé : {fichier}")
        else:
            print(f"ℹ️ Non trouvé : {fichier}")

def main():
    print("🧪 SCRIPT DE TEST - ANALYSEURS BACCARAT")
    print("=" * 80)
    print(f"Heure de début : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Créer le dataset de test
    filename_test = creer_dataset_test()
    
    # Tester les analyseurs
    succes_biais = tester_analyseur_biais(filename_test)
    succes_statistique = tester_analyseur_statistique(filename_test)
    
    # Résumé des tests
    print(f"\n📊 RÉSUMÉ DES TESTS")
    print("=" * 50)
    print(f"🔍 Analyseur de biais systématique : {'✅ SUCCÈS' if succes_biais else '❌ ÉCHEC'}")
    print(f"📊 Analyseur statistique avancé : {'✅ SUCCÈS' if succes_statistique else '❌ ÉCHEC'}")
    
    if succes_biais and succes_statistique:
        print(f"\n🎉 TOUS LES TESTS RÉUSSIS !")
        print("✅ Les analyseurs sont prêts à utiliser")
        print("\n🚀 UTILISATION :")
        print("   python lancer_analyse_complete.py dataset_baccarat_lupasco_20250704_170242_condensed.json")
    else:
        print(f"\n⚠️ CERTAINS TESTS ONT ÉCHOUÉ")
        if not succes_statistique:
            print("💡 Pour l'analyseur statistique, installez les dépendances :")
            print("   python installer_dependances.py")
    
    # Nettoyage
    nettoyer_fichiers_test()
    
    print(f"\n✅ Tests terminés : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
